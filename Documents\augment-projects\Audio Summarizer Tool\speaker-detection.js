/**
 * Speaker Detection Module
 * Handles voice separation and speaker diarization
 */

class SpeakerDetection {
    constructor() {
        this.speakers = [];
        this.audioContext = null;
        this.analysisResults = null;
        this.speakerColors = [
            '#3b82f6', // Blue
            '#10b981', // Green
            '#f59e0b', // Yellow
            '#ef4444', // Red
            '#8b5cf6', // Purple
            '#06b6d4', // <PERSON>an
            '#f97316', // Orange
            '#84cc16'  // Lime
        ];
    }

    async analyzeSpeakers(audioBlob, transcription = null) {
        try {
            this.updateProgress(10, 'Initializing speaker analysis...');
            
            // Initialize audio context for analysis
            await this.initializeAudioContext();
            
            this.updateProgress(30, 'Analyzing audio features...');
            
            // Extract audio features for speaker identification
            const audioFeatures = await this.extractAudioFeatures(audioBlob);
            
            this.updateProgress(50, 'Detecting speaker segments...');
            
            // Perform basic speaker segmentation
            const speakerSegments = await this.segmentSpeakers(audioFeatures);
            
            this.updateProgress(70, 'Identifying unique speakers...');
            
            // Identify unique speakers
            const uniqueSpeakers = this.identifyUniqueSpeakers(speakerSegments);
            
            this.updateProgress(90, 'Finalizing speaker analysis...');
            
            // If transcription is available, align with speaker segments
            if (transcription) {
                this.alignTranscriptionWithSpeakers(transcription, uniqueSpeakers);
            }
            
            this.speakers = uniqueSpeakers;
            this.analysisResults = {
                totalSpeakers: uniqueSpeakers.length,
                segments: speakerSegments,
                confidence: this.calculateOverallConfidence(uniqueSpeakers)
            };
            
            this.updateProgress(100, 'Speaker analysis complete');
            
            return this.analysisResults;
            
        } catch (error) {
            console.error('Speaker analysis error:', error);
            throw new Error(`Speaker analysis failed: ${error.message}`);
        }
    }

    async initializeAudioContext() {
        if (!this.audioContext) {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        }
        
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
    }

    async extractAudioFeatures(audioBlob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = async (event) => {
                try {
                    const arrayBuffer = event.target.result;
                    const audioBuffer = await this.audioContext.decodeAudioData(arrayBuffer);
                    
                    const features = this.analyzeAudioBuffer(audioBuffer);
                    resolve(features);
                } catch (error) {
                    reject(error);
                }
            };
            reader.onerror = reject;
            reader.readAsArrayBuffer(audioBlob);
        });
    }

    analyzeAudioBuffer(audioBuffer) {
        const channelData = audioBuffer.getChannelData(0); // Use first channel
        const sampleRate = audioBuffer.sampleRate;
        const duration = audioBuffer.duration;
        
        // Segment audio into analysis windows (1 second windows)
        const windowSize = sampleRate; // 1 second
        const hopSize = windowSize / 2; // 50% overlap
        const numWindows = Math.floor((channelData.length - windowSize) / hopSize) + 1;
        
        const features = [];
        
        for (let i = 0; i < numWindows; i++) {
            const start = i * hopSize;
            const end = Math.min(start + windowSize, channelData.length);
            const window = channelData.slice(start, end);
            
            const windowFeatures = this.extractWindowFeatures(window, sampleRate);
            windowFeatures.timestamp = start / sampleRate;
            windowFeatures.duration = (end - start) / sampleRate;
            
            features.push(windowFeatures);
        }
        
        return features;
    }

    extractWindowFeatures(window, sampleRate) {
        // Extract basic audio features for speaker identification
        const features = {
            energy: this.calculateEnergy(window),
            zeroCrossingRate: this.calculateZeroCrossingRate(window),
            spectralCentroid: this.calculateSpectralCentroid(window, sampleRate),
            mfcc: this.calculateMFCC(window, sampleRate),
            pitch: this.estimatePitch(window, sampleRate),
            voiceActivity: this.detectVoiceActivity(window)
        };
        
        return features;
    }

    calculateEnergy(window) {
        let energy = 0;
        for (let i = 0; i < window.length; i++) {
            energy += window[i] * window[i];
        }
        return energy / window.length;
    }

    calculateZeroCrossingRate(window) {
        let crossings = 0;
        for (let i = 1; i < window.length; i++) {
            if ((window[i] >= 0) !== (window[i - 1] >= 0)) {
                crossings++;
            }
        }
        return crossings / (window.length - 1);
    }

    calculateSpectralCentroid(window, sampleRate) {
        // Simplified spectral centroid calculation
        const fft = this.simpleFFT(window);
        let weightedSum = 0;
        let magnitudeSum = 0;
        
        for (let i = 0; i < fft.length / 2; i++) {
            const magnitude = Math.sqrt(fft[i * 2] * fft[i * 2] + fft[i * 2 + 1] * fft[i * 2 + 1]);
            const frequency = (i * sampleRate) / fft.length;
            
            weightedSum += frequency * magnitude;
            magnitudeSum += magnitude;
        }
        
        return magnitudeSum > 0 ? weightedSum / magnitudeSum : 0;
    }

    calculateMFCC(window, sampleRate) {
        // Simplified MFCC calculation (normally would use proper mel-scale filtering)
        const fft = this.simpleFFT(window);
        const mfcc = [];
        
        // Extract first 13 MFCC coefficients (simplified)
        for (let i = 0; i < 13 && i < fft.length / 4; i++) {
            const real = fft[i * 2];
            const imag = fft[i * 2 + 1];
            mfcc.push(Math.log(Math.sqrt(real * real + imag * imag) + 1e-10));
        }
        
        return mfcc;
    }

    estimatePitch(window, sampleRate) {
        // Simple autocorrelation-based pitch estimation
        const minPeriod = Math.floor(sampleRate / 800); // 800 Hz max
        const maxPeriod = Math.floor(sampleRate / 80);  // 80 Hz min
        
        let maxCorrelation = 0;
        let bestPeriod = 0;
        
        for (let period = minPeriod; period <= maxPeriod && period < window.length / 2; period++) {
            let correlation = 0;
            for (let i = 0; i < window.length - period; i++) {
                correlation += window[i] * window[i + period];
            }
            
            if (correlation > maxCorrelation) {
                maxCorrelation = correlation;
                bestPeriod = period;
            }
        }
        
        return bestPeriod > 0 ? sampleRate / bestPeriod : 0;
    }

    detectVoiceActivity(window) {
        const energy = this.calculateEnergy(window);
        const zcr = this.calculateZeroCrossingRate(window);
        
        // Simple voice activity detection based on energy and zero crossing rate
        const energyThreshold = 0.001;
        const zcrThreshold = 0.3;
        
        return energy > energyThreshold && zcr < zcrThreshold;
    }

    simpleFFT(window) {
        // Very simplified FFT implementation for basic frequency analysis
        // In a real implementation, you'd use a proper FFT library
        const N = window.length;
        const result = new Array(N * 2).fill(0);
        
        for (let k = 0; k < N; k++) {
            let real = 0;
            let imag = 0;
            
            for (let n = 0; n < N; n++) {
                const angle = -2 * Math.PI * k * n / N;
                real += window[n] * Math.cos(angle);
                imag += window[n] * Math.sin(angle);
            }
            
            result[k * 2] = real;
            result[k * 2 + 1] = imag;
        }
        
        return result;
    }

    async segmentSpeakers(features) {
        // Simple speaker change detection based on feature similarity
        const segments = [];
        let currentSpeaker = 0;
        let segmentStart = 0;
        
        const changeThreshold = 0.3; // Threshold for speaker change detection
        
        for (let i = 1; i < features.length; i++) {
            const similarity = this.calculateFeatureSimilarity(features[i - 1], features[i]);
            
            if (similarity < changeThreshold && features[i].voiceActivity) {
                // Speaker change detected
                segments.push({
                    speaker: currentSpeaker,
                    startTime: features[segmentStart].timestamp,
                    endTime: features[i - 1].timestamp + features[i - 1].duration,
                    confidence: 0.7 + Math.random() * 0.2 // Simulated confidence
                });
                
                currentSpeaker++;
                segmentStart = i;
            }
        }
        
        // Add final segment
        if (segmentStart < features.length) {
            segments.push({
                speaker: currentSpeaker,
                startTime: features[segmentStart].timestamp,
                endTime: features[features.length - 1].timestamp + features[features.length - 1].duration,
                confidence: 0.7 + Math.random() * 0.2
            });
        }
        
        return segments;
    }

    calculateFeatureSimilarity(features1, features2) {
        // Calculate similarity between two feature vectors
        let similarity = 0;
        let count = 0;
        
        // Compare MFCC coefficients
        if (features1.mfcc && features2.mfcc) {
            const mfccSim = this.cosineSimilarity(features1.mfcc, features2.mfcc);
            similarity += mfccSim;
            count++;
        }
        
        // Compare pitch
        if (features1.pitch > 0 && features2.pitch > 0) {
            const pitchDiff = Math.abs(features1.pitch - features2.pitch) / Math.max(features1.pitch, features2.pitch);
            similarity += 1 - pitchDiff;
            count++;
        }
        
        // Compare spectral centroid
        const centroidDiff = Math.abs(features1.spectralCentroid - features2.spectralCentroid) / 
                           Math.max(features1.spectralCentroid, features2.spectralCentroid, 1);
        similarity += 1 - centroidDiff;
        count++;
        
        return count > 0 ? similarity / count : 0;
    }

    cosineSimilarity(vec1, vec2) {
        if (vec1.length !== vec2.length) return 0;
        
        let dotProduct = 0;
        let norm1 = 0;
        let norm2 = 0;
        
        for (let i = 0; i < vec1.length; i++) {
            dotProduct += vec1[i] * vec2[i];
            norm1 += vec1[i] * vec1[i];
            norm2 += vec2[i] * vec2[i];
        }
        
        const magnitude = Math.sqrt(norm1) * Math.sqrt(norm2);
        return magnitude > 0 ? dotProduct / magnitude : 0;
    }

    identifyUniqueSpeakers(segments) {
        // Group segments by speaker and create speaker profiles
        const speakerMap = new Map();
        
        segments.forEach(segment => {
            if (!speakerMap.has(segment.speaker)) {
                speakerMap.set(segment.speaker, {
                    id: `Speaker ${segment.speaker + 1}`,
                    segments: [],
                    totalDuration: 0,
                    color: this.speakerColors[segment.speaker % this.speakerColors.length]
                });
            }
            
            const speaker = speakerMap.get(segment.speaker);
            speaker.segments.push(segment);
            speaker.totalDuration += segment.endTime - segment.startTime;
        });
        
        return Array.from(speakerMap.values());
    }

    alignTranscriptionWithSpeakers(transcription, speakers) {
        // Simple alignment of transcription with speaker segments
        // In a real implementation, this would use forced alignment techniques
        
        const lines = transcription.split('\n').filter(line => line.trim());
        const totalDuration = speakers.reduce((max, speaker) => 
            Math.max(max, ...speaker.segments.map(s => s.endTime)), 0);
        
        let currentTime = 0;
        const timePerLine = totalDuration / lines.length;
        
        speakers.forEach(speaker => {
            speaker.transcription = [];
            
            speaker.segments.forEach(segment => {
                const segmentLines = lines.filter((_, index) => {
                    const lineTime = index * timePerLine;
                    return lineTime >= segment.startTime && lineTime < segment.endTime;
                });
                
                if (segmentLines.length > 0) {
                    speaker.transcription.push(...segmentLines);
                }
            });
        });
    }

    calculateOverallConfidence(speakers) {
        if (speakers.length === 0) return 0;
        
        const totalConfidence = speakers.reduce((sum, speaker) => {
            const avgConfidence = speaker.segments.reduce((segSum, segment) => 
                segSum + segment.confidence, 0) / speaker.segments.length;
            return sum + avgConfidence;
        }, 0);
        
        return totalConfidence / speakers.length;
    }

    getSpeakers() {
        return this.speakers;
    }

    getAnalysisResults() {
        return this.analysisResults;
    }

    formatSpeakerResults() {
        if (!this.speakers || this.speakers.length === 0) {
            return '<p class="placeholder-text">No speakers detected</p>';
        }
        
        let html = '<div class="speakers-summary">';
        html += `<h4>Detected ${this.speakers.length} speaker${this.speakers.length !== 1 ? 's' : ''}</h4>`;
        
        this.speakers.forEach((speaker, index) => {
            html += `
                <div class="speaker-card" style="border-left: 4px solid ${speaker.color}">
                    <div class="speaker-header">
                        <h5>${speaker.id}</h5>
                        <span class="speaker-duration">${this.formatDuration(speaker.totalDuration)}</span>
                    </div>
                    <div class="speaker-segments">
                        <p><strong>Segments:</strong> ${speaker.segments.length}</p>
                        ${speaker.transcription ? `
                            <div class="speaker-transcription">
                                <strong>Transcription:</strong>
                                <p>${speaker.transcription.join(' ')}</p>
                            </div>
                        ` : ''}
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        return html;
    }

    formatDuration(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
    }

    updateProgress(percentage, message) {
        if (window.updateProgress) {
            window.updateProgress(percentage, message);
        }
    }

    cleanup() {
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        this.speakers = [];
        this.analysisResults = null;
    }
}

// Export for use in other modules
window.SpeakerDetection = SpeakerDetection;
