{"name": "audio-summarizer-tool", "version": "1.0.0", "description": "A web application for recording, transcribing, and summarizing audio using AI", "main": "index.html", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "serve": "http-server . -p 3000 -c-1 --cors", "serve-https": "http-server . -p 3000 -c-1 --cors -S -C cert.pem -K key.pem", "start": "npm run dev"}, "keywords": ["audio", "transcription", "ai", "summarization", "speech-to-text", "gemini"], "author": "", "license": "MIT", "devDependencies": {"vite": "^5.0.0", "http-server": "^14.1.1"}, "engines": {"node": ">=16.0.0"}}