/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    /* Color Palette - Black Noir with Blue Accents */
    --primary-black: #0a0a0a;
    --secondary-black: #1a1a1a;
    --tertiary-black: #2a2a2a;
    --accent-blue: #3b82f6;
    --accent-blue-hover: #2563eb;
    --accent-blue-light: #60a5fa;
    --text-primary: #ffffff;
    --text-secondary: #a1a1aa;
    --text-muted: #71717a;
    --border-color: #374151;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --error-color: #ef4444;
    
    /* Typography */
    --font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    --font-size-xs: 0.75rem;
    --font-size-sm: 0.875rem;
    --font-size-base: 1rem;
    --font-size-lg: 1.125rem;
    --font-size-xl: 1.25rem;
    --font-size-2xl: 1.5rem;
    --font-size-3xl: 1.875rem;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    --spacing-2xl: 3rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
}

body {
    font-family: var(--font-family);
    background: linear-gradient(135deg, var(--primary-black) 0%, var(--secondary-black) 100%);
    color: var(--text-primary);
    line-height: 1.6;
    min-height: 100vh;
}

/* App Container */
.app-container {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* Header */
.app-header {
    background: var(--secondary-black);
    border-bottom: 1px solid var(--border-color);
    padding: var(--spacing-lg) var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
}

.app-title {
    font-size: var(--font-size-2xl);
    font-weight: 700;
    color: var(--text-primary);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.app-title i {
    color: var(--accent-blue);
}

.header-controls {
    display: flex;
    gap: var(--spacing-md);
}

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    gap: var(--spacing-sm);
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    border-radius: var(--radius-md);
    font-size: var(--font-size-base);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    background: transparent;
    color: var(--text-primary);
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background: var(--accent-blue);
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background: var(--accent-blue-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-secondary {
    background: var(--tertiary-black);
    border: 1px solid var(--border-color);
}

.btn-secondary:hover:not(:disabled) {
    background: var(--border-color);
    transform: translateY(-1px);
}

.btn-record {
    background: var(--success-color);
    color: white;
}

.btn-record:hover:not(:disabled) {
    background: #059669;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-stop {
    background: var(--error-color);
    color: white;
}

.btn-stop:hover:not(:disabled) {
    background: #dc2626;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.btn-pause {
    background: var(--warning-color);
    color: white;
}

.btn-pause:hover:not(:disabled) {
    background: #d97706;
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg);
}

.icon-btn {
    padding: var(--spacing-md);
    border-radius: var(--radius-md);
    background: var(--tertiary-black);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    cursor: pointer;
    transition: all 0.2s ease;
}

.icon-btn:hover {
    background: var(--border-color);
    color: var(--accent-blue);
    transform: translateY(-1px);
}

/* Main Content */
.main-content {
    flex: 1;
    padding: var(--spacing-xl);
    max-width: 1200px;
    margin: 0 auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-xl);
}

/* Panels */
.config-panel,
.audio-controls,
.processing-panel,
.results-panel {
    background: var(--secondary-black);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-lg);
    padding: var(--spacing-xl);
    box-shadow: var(--shadow-md);
}

.config-panel {
    display: none;
}

.config-panel.active {
    display: block;
}

.config-content h3,
.control-group h3,
.processing-options h3,
.results-header h3 {
    font-size: var(--font-size-xl);
    font-weight: 600;
    margin-bottom: var(--spacing-lg);
    color: var(--text-primary);
}

/* Input Groups */
.input-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

.input-group label {
    font-size: var(--font-size-sm);
    font-weight: 500;
    color: var(--text-secondary);
}

.input-group input[type="password"],
.input-group input[type="text"] {
    padding: var(--spacing-md);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    background: var(--tertiary-black);
    color: var(--text-primary);
    font-size: var(--font-size-base);
    transition: border-color 0.2s ease;
}

.input-group input:focus {
    outline: none;
    border-color: var(--accent-blue);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-note {
    font-size: var(--font-size-sm);
    color: var(--text-muted);
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
}

/* Checkbox Styles */
.checkbox-label {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    cursor: pointer;
    font-size: var(--font-size-base);
    color: var(--text-secondary);
    margin-bottom: var(--spacing-md);
}

.checkbox-label input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 20px;
    height: 20px;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    transition: all 0.2s ease;
}

.checkbox-label input[type="checkbox"]:checked + .checkmark {
    background: var(--accent-blue);
    border-color: var(--accent-blue);
}

.checkbox-label input[type="checkbox"]:checked + .checkmark::after {
    content: '✓';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: var(--font-size-sm);
    font-weight: bold;
}

/* Audio Controls */
.input-options {
    display: flex;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-lg);
}

.recording-controls {
    display: flex;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
}

.audio-visualizer {
    background: var(--tertiary-black);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);
    display: flex;
    justify-content: center;
    align-items: center;
}

#audioCanvas {
    max-width: 100%;
    height: auto;
    border-radius: var(--radius-sm);
}

.recording-status {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: var(--spacing-lg);
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
}

.status-text {
    font-size: var(--font-size-base);
    color: var(--text-secondary);
}

.recording-time {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--accent-blue);
    font-family: 'Courier New', monospace;
}

.audio-levels {
    display: flex;
    gap: var(--spacing-lg);
}

.level-meter {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-sm);
}

.level-bar {
    width: 60px;
    height: 8px;
    background: var(--tertiary-black);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    position: relative;
    overflow: hidden;
}

.level-bar::after {
    content: '';
    position: absolute;
    left: 0;
    top: 0;
    height: 100%;
    width: var(--level, 0%);
    background: linear-gradient(90deg, var(--success-color), var(--warning-color), var(--error-color));
    transition: width 0.1s ease;
}

.level-meter label {
    font-size: var(--font-size-xs);
    color: var(--text-muted);
}

/* Processing Panel */
.option-group {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-lg);
}

/* Results Panel */
.results-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-lg);
    flex-wrap: wrap;
    gap: var(--spacing-md);
}

.results-controls {
    display: flex;
    gap: var(--spacing-md);
}

/* Tabs */
.tabs {
    display: flex;
    border-bottom: 1px solid var(--border-color);
    margin-bottom: var(--spacing-lg);
    overflow-x: auto;
}

.tab-btn {
    padding: var(--spacing-md) var(--spacing-lg);
    border: none;
    background: transparent;
    color: var(--text-secondary);
    cursor: pointer;
    font-size: var(--font-size-base);
    font-weight: 500;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
    white-space: nowrap;
}

.tab-btn:hover {
    color: var(--text-primary);
}

.tab-btn.active {
    color: var(--accent-blue);
    border-bottom-color: var(--accent-blue);
}

.tab-panel {
    display: none;
}

.tab-panel.active {
    display: block;
}

.content-area {
    min-height: 200px;
    background: var(--tertiary-black);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
}

.placeholder-text {
    color: var(--text-muted);
    font-style: italic;
    text-align: center;
    margin-top: var(--spacing-xl);
}

/* Podcast Controls */
.podcast-controls {
    margin-bottom: var(--spacing-lg);
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: center;
}

#podcastPlayer {
    width: 100%;
    max-width: 500px;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(10, 10, 10, 0.9);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.loading-overlay.active {
    display: flex;
}

.loading-content {
    text-align: center;
    max-width: 400px;
    padding: var(--spacing-xl);
}

.spinner {
    width: 50px;
    height: 50px;
    border: 3px solid var(--border-color);
    border-top: 3px solid var(--accent-blue);
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto var(--spacing-lg);
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

#loadingText {
    font-size: var(--font-size-lg);
    color: var(--text-primary);
    margin-bottom: var(--spacing-lg);
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: var(--tertiary-black);
    border-radius: var(--radius-sm);
    overflow: hidden;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, var(--accent-blue), var(--accent-blue-light));
    width: 0%;
    transition: width 0.3s ease;
}

/* Toast Notifications */
.toast-container {
    position: fixed;
    top: var(--spacing-xl);
    right: var(--spacing-xl);
    z-index: 1001;
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
}

.toast {
    background: var(--secondary-black);
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    padding: var(--spacing-lg);
    box-shadow: var(--shadow-xl);
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    min-width: 300px;
    animation: slideIn 0.3s ease;
}

.toast.success {
    border-left: 4px solid var(--success-color);
}

.toast.error {
    border-left: 4px solid var(--error-color);
}

.toast.warning {
    border-left: 4px solid var(--warning-color);
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .main-content {
        padding: var(--spacing-lg);
        gap: var(--spacing-lg);
    }
    
    .app-header {
        padding: var(--spacing-md) var(--spacing-lg);
    }
    
    .header-content {
        flex-direction: column;
        gap: var(--spacing-md);
        text-align: center;
    }
    
    .recording-controls {
        justify-content: center;
    }
    
    .input-options {
        flex-direction: column;
        gap: var(--spacing-md);
    }
    
    .recording-status {
        flex-direction: column;
        text-align: center;
    }
    
    .results-header {
        flex-direction: column;
        align-items: stretch;
    }
    
    .results-controls {
        justify-content: center;
    }
    
    .toast-container {
        left: var(--spacing-md);
        right: var(--spacing-md);
    }
    
    .toast {
        min-width: auto;
    }
}

@media (max-width: 480px) {
    .app-title {
        font-size: var(--font-size-xl);
    }
    
    .recording-controls {
        flex-direction: column;
    }
    
    .btn {
        justify-content: center;
    }
    
    .tabs {
        justify-content: center;
    }
    
    .tab-btn {
        flex: 1;
        text-align: center;
    }
}
