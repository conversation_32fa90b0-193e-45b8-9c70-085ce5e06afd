<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Audio Recording Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        button {
            padding: 10px 20px;
            margin: 5px;
            border: none;
            border-radius: 4px;
            background: #007bff;
            color: white;
            cursor: pointer;
        }
        button:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        .status {
            margin: 10px 0;
            padding: 10px;
            border-radius: 4px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .warning { background: #fff3cd; color: #856404; }
        .info { background: #d1ecf1; color: #0c5460; }
    </style>
</head>
<body>
    <h1>Audio Recording Test</h1>
    
    <div class="test-section">
        <h2>Browser Compatibility</h2>
        <button onclick="checkCompatibility()">Check Compatibility</button>
        <div id="compatibilityResult"></div>
    </div>
    
    <div class="test-section">
        <h2>Microphone Test</h2>
        <button onclick="testMicrophone()">Test Microphone</button>
        <button onclick="stopMicrophone()" disabled id="stopMicBtn">Stop Microphone</button>
        <div id="microphoneResult"></div>
    </div>
    
    <div class="test-section">
        <h2>System Audio Test</h2>
        <button onclick="testSystemAudio()">Test System Audio</button>
        <button onclick="stopSystemAudio()" disabled id="stopSystemBtn">Stop System Audio</button>
        <div id="systemAudioResult"></div>
        <p><strong>Note:</strong> You must select "Share system audio" in the screen share dialog for this to work.</p>
    </div>
    
    <div class="test-section">
        <h2>Recording Test</h2>
        <label>
            <input type="checkbox" id="useMic" checked> Use Microphone
        </label>
        <label>
            <input type="checkbox" id="useSystem"> Use System Audio
        </label>
        <br><br>
        <button onclick="startRecording()">Start Recording</button>
        <button onclick="stopRecording()" disabled id="stopRecBtn">Stop Recording</button>
        <div id="recordingResult"></div>
        <audio id="playback" controls style="display: none; width: 100%; margin-top: 10px;"></audio>
    </div>

    <script>
        let micStream = null;
        let systemStream = null;
        let mediaRecorder = null;
        let recordedChunks = [];

        function showStatus(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.innerHTML = `<div class="status ${type}">${message}</div>`;
        }

        function checkCompatibility() {
            const issues = [];
            const features = [];
            
            if (!navigator.mediaDevices) {
                issues.push('MediaDevices API not supported');
            } else {
                features.push('MediaDevices API supported');
            }
            
            if (!navigator.mediaDevices?.getUserMedia) {
                issues.push('getUserMedia not supported');
            } else {
                features.push('getUserMedia supported');
            }
            
            if (!navigator.mediaDevices?.getDisplayMedia) {
                issues.push('getDisplayMedia not supported');
            } else {
                features.push('getDisplayMedia supported');
            }
            
            if (!window.MediaRecorder) {
                issues.push('MediaRecorder not supported');
            } else {
                features.push('MediaRecorder supported');
            }
            
            if (!window.AudioContext && !window.webkitAudioContext) {
                issues.push('Web Audio API not supported');
            } else {
                features.push('Web Audio API supported');
            }
            
            if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
                issues.push('HTTPS required for media access');
            } else {
                features.push('Secure context available');
            }
            
            let result = '<h3>Supported Features:</h3><ul>';
            features.forEach(feature => result += `<li>${feature}</li>`);
            result += '</ul>';
            
            if (issues.length > 0) {
                result += '<h3>Issues:</h3><ul>';
                issues.forEach(issue => result += `<li>${issue}</li>`);
                result += '</ul>';
            }
            
            showStatus('compatibilityResult', result, issues.length > 0 ? 'warning' : 'success');
        }

        async function testMicrophone() {
            try {
                micStream = await navigator.mediaDevices.getUserMedia({
                    audio: {
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                });
                
                showStatus('microphoneResult', 'Microphone access granted successfully!', 'success');
                document.getElementById('stopMicBtn').disabled = false;
                
            } catch (error) {
                showStatus('microphoneResult', `Microphone access failed: ${error.message}`, 'error');
            }
        }

        function stopMicrophone() {
            if (micStream) {
                micStream.getTracks().forEach(track => track.stop());
                micStream = null;
                document.getElementById('stopMicBtn').disabled = true;
                showStatus('microphoneResult', 'Microphone stopped', 'info');
            }
        }

        async function testSystemAudio() {
            try {
                systemStream = await navigator.mediaDevices.getDisplayMedia({
                    video: {
                        mediaSource: 'screen',
                        width: { max: 1 },
                        height: { max: 1 }
                    },
                    audio: {
                        echoCancellation: false,
                        noiseSuppression: false,
                        autoGainControl: false
                    }
                });
                
                const audioTracks = systemStream.getAudioTracks();
                if (audioTracks.length === 0) {
                    throw new Error('No audio tracks found. Make sure to select "Share system audio".');
                }
                
                // Stop video tracks
                const videoTracks = systemStream.getVideoTracks();
                videoTracks.forEach(track => {
                    track.stop();
                    systemStream.removeTrack(track);
                });
                
                showStatus('systemAudioResult', `System audio access granted! Found ${audioTracks.length} audio track(s).`, 'success');
                document.getElementById('stopSystemBtn').disabled = false;
                
            } catch (error) {
                showStatus('systemAudioResult', `System audio access failed: ${error.message}`, 'error');
            }
        }

        function stopSystemAudio() {
            if (systemStream) {
                systemStream.getTracks().forEach(track => track.stop());
                systemStream = null;
                document.getElementById('stopSystemBtn').disabled = true;
                showStatus('systemAudioResult', 'System audio stopped', 'info');
            }
        }

        async function startRecording() {
            try {
                const useMic = document.getElementById('useMic').checked;
                const useSystem = document.getElementById('useSystem').checked;
                
                if (!useMic && !useSystem) {
                    showStatus('recordingResult', 'Please select at least one audio source', 'warning');
                    return;
                }
                
                const streams = [];
                
                if (useMic) {
                    if (!micStream) {
                        await testMicrophone();
                    }
                    if (micStream) streams.push(micStream);
                }
                
                if (useSystem) {
                    if (!systemStream) {
                        await testSystemAudio();
                    }
                    if (systemStream) streams.push(systemStream);
                }
                
                if (streams.length === 0) {
                    showStatus('recordingResult', 'No audio streams available', 'error');
                    return;
                }
                
                // Create combined stream
                const combinedStream = new MediaStream();
                streams.forEach(stream => {
                    stream.getAudioTracks().forEach(track => {
                        combinedStream.addTrack(track);
                    });
                });
                
                recordedChunks = [];
                mediaRecorder = new MediaRecorder(combinedStream);
                
                mediaRecorder.ondataavailable = (event) => {
                    if (event.data.size > 0) {
                        recordedChunks.push(event.data);
                    }
                };
                
                mediaRecorder.onstop = () => {
                    const blob = new Blob(recordedChunks, { type: 'audio/webm' });
                    const url = URL.createObjectURL(blob);
                    
                    const audio = document.getElementById('playback');
                    audio.src = url;
                    audio.style.display = 'block';
                    
                    showStatus('recordingResult', `Recording completed! Size: ${(blob.size / 1024).toFixed(2)} KB`, 'success');
                    document.getElementById('stopRecBtn').disabled = true;
                };
                
                mediaRecorder.start();
                showStatus('recordingResult', 'Recording started...', 'info');
                document.getElementById('stopRecBtn').disabled = false;
                
            } catch (error) {
                showStatus('recordingResult', `Recording failed: ${error.message}`, 'error');
            }
        }

        function stopRecording() {
            if (mediaRecorder && mediaRecorder.state === 'recording') {
                mediaRecorder.stop();
            }
        }

        // Initialize
        checkCompatibility();
    </script>
</body>
</html>
