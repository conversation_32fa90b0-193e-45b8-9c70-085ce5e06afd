# Audio Summarizer Tool

A comprehensive web application for real-time audio capture and AI-powered summarization with podcast generation capabilities.

## Features

### 🎤 Audio Capture
- **Dual Input Support**: Capture from microphone and system audio simultaneously
- **Real-time Visualization**: Live audio waveform display during recording
- **Audio Level Monitoring**: Visual feedback for microphone and system audio levels
- **Recording Controls**: Start, stop, pause, and resume functionality

### 🤖 AI Integration
- **Google Gemini 2.0 Flash**: Advanced audio transcription and summarization
- **Speaker Identification**: Automatic detection and labeling of multiple speakers
- **Intelligent Summarization**: Key points extraction and content analysis
- **Real-time Processing**: Optional live processing during recording

### 🎧 Podcast Generation
- **Gemini TTS**: High-quality text-to-speech using Gemini 2.5 Flash TTS model
- **30 Premium Voices**: Choose from Gemini's professional voice collection
- **Multi-language Support**: 24 languages with automatic detection
- **Download Support**: Export generated podcasts as high-quality audio files

### 🎨 Modern UI/UX
- **Black Noir Theme**: Sleek dark interface with blue accents
- **Responsive Design**: Works on desktop and mobile devices
- **Intuitive Controls**: Easy-to-use interface with visual feedback
- **Progress Indicators**: Real-time feedback during processing

## Setup Instructions

### Prerequisites
- Modern web browser (Chrome, Firefox, Safari, Edge)
- Google Gemini API key ([Get one here](https://makersuite.google.com/app/apikey))
- HTTPS connection (required for microphone access)

### Installation

1. **Clone or Download** the project files to your local machine
2. **Serve the files** using a local web server (required for security features):

   **Option A: Using Python**
   ```bash
   # Python 3
   python -m http.server 8000
   
   # Python 2
   python -m SimpleHTTPServer 8000
   ```

   **Option B: Using Node.js**
   ```bash
   npx http-server -p 8000
   ```

   **Option C: Using Live Server (VS Code)**
   - Install the "Live Server" extension
   - Right-click on `index.html` and select "Open with Live Server"

3. **Open your browser** and navigate to `http://localhost:8000`

### API Configuration

1. **Get a Gemini API Key**:
   - Visit [Google AI Studio](https://makersuite.google.com/app/apikey)
   - Create a new API key
   - Copy the key for use in the application

2. **Configure the Application**:
   - Click the settings icon (⚙️) in the top-right corner
   - Paste your Gemini API key
   - Click "Save" to validate and store the key

## Usage Guide

### Recording Audio

1. **Select Input Sources**:
   - ✅ Microphone: Capture your voice or ambient audio
   - ✅ System Audio: Capture computer audio (limited browser support)

2. **Start Recording**:
   - Click "Start Recording" or use `Ctrl+R`
   - Grant microphone permissions when prompted
   - Monitor audio levels and waveform visualization

3. **Control Recording**:
   - **Pause/Resume**: Use the pause button or `Ctrl+P`
   - **Stop**: Click "Stop Recording" or `Ctrl+R` again

### Processing Audio

1. **Configure Options**:
   - ✅ Real-time Processing: Process audio as it's recorded
   - ✅ Speaker Identification: Detect and label different speakers
   - ✅ Generate Podcast: Prepare for podcast creation

2. **Process with AI**:
   - Click "Process with AI" or use `Ctrl+Enter`
   - Wait for transcription, summarization, and speaker analysis
   - View results in the tabs below

### Generating Podcasts

1. **Process audio first** to generate a summary
2. **Navigate to the Podcast tab**
3. **Click "Generate Podcast"**
4. **Listen to the preview** and download if satisfied

### Exporting Results

- **Export All**: Download a JSON file with all results
- **Download Podcast**: Save the generated audio file
- **Copy Text**: Select and copy transcription or summary text

## Browser Compatibility

### Fully Supported
- ✅ Chrome 80+
- ✅ Firefox 75+
- ✅ Safari 14+
- ✅ Edge 80+

### Limitations
- **System Audio Capture**: Limited to Chrome/Edge with screen sharing
- **Microphone Access**: Requires HTTPS in production
- **Speech Synthesis**: Voice quality varies by browser and OS

## Troubleshooting

### Common Issues

**"Microphone access denied"**
- Ensure you're using HTTPS or localhost
- Check browser permissions for microphone access
- Try refreshing the page and granting permissions again

**"API key validation failed"**
- Verify your Gemini API key is correct
- Check your internet connection
- Ensure the API key has proper permissions

**"System audio not working"**
- System audio capture requires screen sharing in most browsers
- Select "Share audio" when prompted for screen sharing
- This feature has limited browser support

**"Podcast generation failed"**
- Ensure your Gemini API key has access to TTS models
- Check that you're using a supported voice name
- Verify your internet connection for API calls

### Performance Tips

- **Close other tabs** to free up system resources
- **Use a wired microphone** for better audio quality
- **Ensure stable internet** connection for AI processing
- **Process shorter recordings** (under 10 minutes) for faster results

## Technical Architecture

### File Structure
```
├── index.html              # Main application interface
├── styles.css              # Black noir theme with blue accents
├── app.js                  # Main application controller
├── audio-processor.js      # Web Audio API handling
├── gemini-api.js          # Google Gemini API integration
├── speaker-detection.js    # Voice separation and diarization
├── podcast-generator.js    # TTS and audio generation
└── README.md              # This file
```

### Key Technologies
- **Web Audio API**: Real-time audio processing and visualization
- **MediaRecorder API**: Audio recording and export
- **Speech Synthesis API**: Text-to-speech conversion
- **Google Gemini API**: AI transcription and summarization
- **Canvas API**: Audio waveform visualization

## Security & Privacy

- **Local Storage**: API keys are stored locally in your browser
- **No Server**: All processing happens in your browser or via direct API calls
- **Data Privacy**: Audio data is only sent to Google Gemini for processing
- **Secure Communication**: All API calls use HTTPS encryption

## Limitations & Future Enhancements

### Current Limitations
- System audio capture requires screen sharing
- TTS output can't be directly captured in browsers
- Processing time depends on audio length and internet speed
- Speaker identification accuracy varies with audio quality

### Planned Enhancements
- Cloud TTS integration for better podcast generation
- Advanced speaker diarization algorithms
- Real-time transcription display
- Audio enhancement and noise reduction
- Multiple language support
- Batch processing capabilities

## Support

For issues, questions, or feature requests:
1. Check the troubleshooting section above
2. Verify your browser compatibility
3. Ensure proper API configuration
4. Test with a simple recording first

## License

This project is provided as-is for educational and demonstration purposes. Please ensure compliance with Google Gemini API terms of service when using the AI features.

---

**Note**: This application requires a Google Gemini API key and internet connection for AI features. Audio processing and podcast generation work offline once the initial setup is complete.
