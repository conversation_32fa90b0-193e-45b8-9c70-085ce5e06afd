/**
 * Audio Processor Module
 * Handles Web Audio API functionality for dual audio input capture
 */

class AudioProcessor {
    constructor() {
        this.audioContext = null;
        this.microphoneStream = null;
        this.systemAudioStream = null;
        this.mediaRecorder = null;
        this.recordedChunks = [];
        this.isRecording = false;
        this.isPaused = false;
        this.startTime = null;
        this.pausedTime = 0;
        
        // Audio analysis
        this.micAnalyser = null;
        this.systemAnalyser = null;
        this.micDataArray = null;
        this.systemDataArray = null;
        
        // Visualization
        this.canvas = null;
        this.canvasContext = null;
        this.animationId = null;
        
        // Audio levels
        this.micLevel = 0;
        this.systemLevel = 0;
        
        this.initializeCanvas();
        this.setupEventListeners();
    }

    initializeCanvas() {
        this.canvas = document.getElementById('audioCanvas');
        this.canvasContext = this.canvas.getContext('2d');
        
        // Set canvas size for high DPI displays
        const rect = this.canvas.getBoundingClientRect();
        const dpr = window.devicePixelRatio || 1;
        this.canvas.width = rect.width * dpr;
        this.canvas.height = rect.height * dpr;
        this.canvasContext.scale(dpr, dpr);
        
        this.drawIdleState();
    }

    setupEventListeners() {
        window.addEventListener('resize', () => {
            this.initializeCanvas();
        });
    }

    async initializeAudioContext() {
        try {
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
            }
            
            return true;
        } catch (error) {
            console.error('Failed to initialize audio context:', error);
            this.showToast('Failed to initialize audio system', 'error');
            return false;
        }
    }

    async requestMicrophoneAccess() {
        try {
            const constraints = {
                audio: {
                    echoCancellation: true,
                    noiseSuppression: true,
                    autoGainControl: true,
                    sampleRate: 44100
                }
            };

            this.microphoneStream = await navigator.mediaDevices.getUserMedia(constraints);
            this.setupMicrophoneAnalyser();
            this.showToast('Microphone access granted', 'success');
            return true;
        } catch (error) {
            console.error('Microphone access denied:', error);
            this.showToast('Microphone access denied. Please allow microphone permissions.', 'error');
            return false;
        }
    }

    async requestSystemAudioAccess() {
        try {
            // Note: System audio capture is limited in browsers
            // This attempts to capture display media with audio
            const constraints = {
                video: false,
                audio: {
                    echoCancellation: false,
                    noiseSuppression: false,
                    autoGainControl: false,
                    sampleRate: 44100
                }
            };

            // Try to get display media with audio
            this.systemAudioStream = await navigator.mediaDevices.getDisplayMedia(constraints);
            this.setupSystemAudioAnalyser();
            this.showToast('System audio access granted', 'success');
            return true;
        } catch (error) {
            console.error('System audio access failed:', error);
            this.showToast('System audio capture not available or denied', 'warning');
            return false;
        }
    }

    setupMicrophoneAnalyser() {
        if (!this.audioContext || !this.microphoneStream) return;

        const source = this.audioContext.createMediaStreamSource(this.microphoneStream);
        this.micAnalyser = this.audioContext.createAnalyser();
        this.micAnalyser.fftSize = 2048;
        this.micAnalyser.smoothingTimeConstant = 0.8;
        
        source.connect(this.micAnalyser);
        
        const bufferLength = this.micAnalyser.frequencyBinCount;
        this.micDataArray = new Uint8Array(bufferLength);
    }

    setupSystemAudioAnalyser() {
        if (!this.audioContext || !this.systemAudioStream) return;

        const source = this.audioContext.createMediaStreamSource(this.systemAudioStream);
        this.systemAnalyser = this.audioContext.createAnalyser();
        this.systemAnalyser.fftSize = 2048;
        this.systemAnalyser.smoothingTimeConstant = 0.8;
        
        source.connect(this.systemAnalyser);
        
        const bufferLength = this.systemAnalyser.frequencyBinCount;
        this.systemDataArray = new Uint8Array(bufferLength);
    }

    createMixedStream() {
        const mixedStream = new MediaStream();
        
        if (this.microphoneStream) {
            this.microphoneStream.getAudioTracks().forEach(track => {
                mixedStream.addTrack(track);
            });
        }
        
        if (this.systemAudioStream) {
            this.systemAudioStream.getAudioTracks().forEach(track => {
                mixedStream.addTrack(track);
            });
        }
        
        return mixedStream;
    }

    async startRecording() {
        try {
            if (!await this.initializeAudioContext()) {
                return false;
            }

            const micEnabled = document.getElementById('microphoneInput').checked;
            const systemEnabled = document.getElementById('systemAudioInput').checked;

            if (!micEnabled && !systemEnabled) {
                this.showToast('Please select at least one audio input source', 'warning');
                return false;
            }

            // Request access to selected audio sources
            if (micEnabled && !this.microphoneStream) {
                if (!await this.requestMicrophoneAccess()) {
                    return false;
                }
            }

            if (systemEnabled && !this.systemAudioStream) {
                if (!await this.requestSystemAudioAccess()) {
                    // Continue with just microphone if system audio fails
                    if (!this.microphoneStream) {
                        return false;
                    }
                }
            }

            // Create mixed stream for recording
            const recordingStream = this.createMixedStream();
            
            if (recordingStream.getAudioTracks().length === 0) {
                this.showToast('No audio tracks available for recording', 'error');
                return false;
            }

            // Setup MediaRecorder
            this.mediaRecorder = new MediaRecorder(recordingStream, {
                mimeType: this.getSupportedMimeType()
            });

            this.recordedChunks = [];
            this.mediaRecorder.ondataavailable = (event) => {
                if (event.data.size > 0) {
                    this.recordedChunks.push(event.data);
                }
            };

            this.mediaRecorder.onstop = () => {
                this.handleRecordingStop();
            };

            this.mediaRecorder.onerror = (event) => {
                console.error('MediaRecorder error:', event.error);
                this.showToast('Recording error occurred', 'error');
            };

            // Start recording
            this.mediaRecorder.start(1000); // Collect data every second
            this.isRecording = true;
            this.isPaused = false;
            this.startTime = Date.now();
            this.pausedTime = 0;

            // Start visualization
            this.startVisualization();

            this.showToast('Recording started', 'success');
            return true;

        } catch (error) {
            console.error('Failed to start recording:', error);
            this.showToast('Failed to start recording', 'error');
            return false;
        }
    }

    stopRecording() {
        if (this.mediaRecorder && this.isRecording) {
            this.mediaRecorder.stop();
            this.isRecording = false;
            this.isPaused = false;
            this.stopVisualization();
            this.showToast('Recording stopped', 'success');
        }
    }

    pauseRecording() {
        if (this.mediaRecorder && this.isRecording && !this.isPaused) {
            this.mediaRecorder.pause();
            this.isPaused = true;
            this.pausedTime += Date.now() - this.startTime;
            this.showToast('Recording paused', 'warning');
        }
    }

    resumeRecording() {
        if (this.mediaRecorder && this.isRecording && this.isPaused) {
            this.mediaRecorder.resume();
            this.isPaused = false;
            this.startTime = Date.now();
            this.showToast('Recording resumed', 'success');
        }
    }

    handleRecordingStop() {
        if (this.recordedChunks.length > 0) {
            const blob = new Blob(this.recordedChunks, { 
                type: this.getSupportedMimeType() 
            });
            
            // Store the recorded audio for processing
            this.recordedAudioBlob = blob;
            
            // Enable processing button
            document.getElementById('processAudio').disabled = false;
            
            // Create download link for raw audio
            this.createDownloadLink(blob);
        }
    }

    createDownloadLink(blob) {
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `recording_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
        a.textContent = 'Download Recording';
        a.className = 'btn btn-secondary';
        a.style.display = 'none';
        document.body.appendChild(a);
        
        // Store reference for later use
        this.downloadLink = a;
    }

    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/ogg;codecs=opus'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'audio/webm'; // Fallback
    }

    startVisualization() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
        }
        
        this.visualize();
    }

    stopVisualization() {
        if (this.animationId) {
            cancelAnimationFrame(this.animationId);
            this.animationId = null;
        }
        this.drawIdleState();
    }

    visualize() {
        this.animationId = requestAnimationFrame(() => this.visualize());
        
        this.updateAudioLevels();
        this.drawWaveform();
    }

    updateAudioLevels() {
        // Update microphone level
        if (this.micAnalyser && this.micDataArray) {
            this.micAnalyser.getByteFrequencyData(this.micDataArray);
            const micSum = this.micDataArray.reduce((sum, value) => sum + value, 0);
            this.micLevel = (micSum / this.micDataArray.length) / 255;
            
            const micLevelBar = document.getElementById('micLevel');
            if (micLevelBar) {
                micLevelBar.style.setProperty('--level', `${this.micLevel * 100}%`);
            }
        }
        
        // Update system audio level
        if (this.systemAnalyser && this.systemDataArray) {
            this.systemAnalyser.getByteFrequencyData(this.systemDataArray);
            const systemSum = this.systemDataArray.reduce((sum, value) => sum + value, 0);
            this.systemLevel = (systemSum / this.systemDataArray.length) / 255;
            
            const systemLevelBar = document.getElementById('systemLevel');
            if (systemLevelBar) {
                systemLevelBar.style.setProperty('--level', `${this.systemLevel * 100}%`);
            }
        }
    }

    drawWaveform() {
        const canvas = this.canvas;
        const ctx = this.canvasContext;
        const width = canvas.width / (window.devicePixelRatio || 1);
        const height = canvas.height / (window.devicePixelRatio || 1);
        
        // Clear canvas
        ctx.fillStyle = '#2a2a2a';
        ctx.fillRect(0, 0, width, height);
        
        // Draw microphone waveform
        if (this.micAnalyser && this.micDataArray) {
            this.micAnalyser.getByteTimeDomainData(this.micDataArray);
            this.drawWaveformData(ctx, this.micDataArray, width, height / 2, '#3b82f6', 0);
        }
        
        // Draw system audio waveform
        if (this.systemAnalyser && this.systemDataArray) {
            this.systemAnalyser.getByteTimeDomainData(this.systemDataArray);
            this.drawWaveformData(ctx, this.systemDataArray, width, height / 2, '#10b981', height / 2);
        }
    }

    drawWaveformData(ctx, dataArray, width, height, color, offsetY) {
        ctx.lineWidth = 2;
        ctx.strokeStyle = color;
        ctx.beginPath();
        
        const sliceWidth = width / dataArray.length;
        let x = 0;
        
        for (let i = 0; i < dataArray.length; i++) {
            const v = dataArray[i] / 128.0;
            const y = (v * height / 2) + offsetY + height / 2;
            
            if (i === 0) {
                ctx.moveTo(x, y);
            } else {
                ctx.lineTo(x, y);
            }
            
            x += sliceWidth;
        }
        
        ctx.stroke();
    }

    drawIdleState() {
        const canvas = this.canvas;
        const ctx = this.canvasContext;
        const width = canvas.width / (window.devicePixelRatio || 1);
        const height = canvas.height / (window.devicePixelRatio || 1);
        
        // Clear canvas
        ctx.fillStyle = '#2a2a2a';
        ctx.fillRect(0, 0, width, height);
        
        // Draw idle message
        ctx.fillStyle = '#71717a';
        ctx.font = '16px Inter, sans-serif';
        ctx.textAlign = 'center';
        ctx.fillText('Audio visualization will appear here during recording', width / 2, height / 2);
    }

    getRecordedAudio() {
        return this.recordedAudioBlob;
    }

    cleanup() {
        this.stopRecording();
        
        if (this.microphoneStream) {
            this.microphoneStream.getTracks().forEach(track => track.stop());
            this.microphoneStream = null;
        }
        
        if (this.systemAudioStream) {
            this.systemAudioStream.getTracks().forEach(track => track.stop());
            this.systemAudioStream = null;
        }
        
        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
        
        if (this.downloadLink) {
            document.body.removeChild(this.downloadLink);
            this.downloadLink = null;
        }
    }

    showToast(message, type = 'info') {
        // This will be implemented in the main app.js file
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Export for use in other modules
window.AudioProcessor = AudioProcessor;
