/**
 * Audio Generator Module
 * Handles single speaker audio generation using Gemini TTS
 */

class AudioGenerator {
    constructor(geminiAPI = null) {
        this.isGenerating = false;
        this.geminiAPI = geminiAPI;
        this.currentPodcast = null;
        this.availableVoices = [
            'zephyr', 'puck', 'charon', 'kore', 'fenrir', 'leda', 'orus', 'aoede',
            'callirrhoe', 'autonoe', 'enceladus', 'iapetus', 'umbriel', 'algieba',
            'despina', 'erinome', 'algenib', 'rasalgethi', 'laomedeia', 'achernar',
            'alnilam', 'schedar', 'gacrux', 'pulcherrima', 'achird', 'zubenelgenubi',
            'vindemiatrix', 'sadachbia', 'sadaltager', 'sulafat'
        ];

        this.initializeAPI();
    }

    initializeAPI() {
        // Use provided API instance or create new one
        if (!this.geminiAPI && window.GeminiAPI) {
            this.geminiAPI = new window.GeminiAPI();
        }
    }

    isConfigured() {
        return this.geminiAPI && this.geminiAPI.isConfigured();
    }

    async generateAudio(script, options = {}) {
        if (this.isGenerating) {
            throw new Error('Audio generation already in progress');
        }

        try {
            this.isGenerating = true;
            this.updateProgress(10, 'Preparing audio generation from transcription...');

            // Process the script for single speaker audio
            const processedScript = this.processTranscriptionForSingleSpeaker(script, options);
            console.log('Processed transcription for single speaker TTS, length:', processedScript.length);

            let audioBlob;
            let usedMethod = 'unknown';

            // Try Gemini TTS first if configured
            if (this.isConfigured()) {
                try {
                    this.updateProgress(30, 'Converting text to speech with Gemini TTS...');
                    audioBlob = await this.generateSingleSpeakerAudio(processedScript, options);
                    usedMethod = 'gemini-tts';
                } catch (geminiError) {
                    console.warn('Gemini TTS failed, falling back to Web Speech API:', geminiError.message);
                    this.updateProgress(50, 'Falling back to Web Speech API...');
                    audioBlob = await this.generateSpeechWithWebAPI(processedScript, options);
                    usedMethod = 'web-speech-api';
                }
            } else {
                console.log('Gemini API not configured, using Web Speech API');
                this.updateProgress(30, 'Converting text to speech with Web Speech API...');
                audioBlob = await this.generateSpeechWithWebAPI(processedScript, options);
                usedMethod = 'web-speech-api';
            }

            this.updateProgress(80, 'Saving audio file...');

            // Save the audio file to the project directory
            const fileName = await this.saveAudioFile(audioBlob, options);
            console.log('File saved successfully:', fileName);

            this.updateProgress(90, 'Finalizing audio...');

            // Get audio duration with error handling
            let duration;
            try {
                console.log('Getting audio duration...');
                duration = await Promise.race([
                    this.getAudioDuration(audioBlob),
                    new Promise(resolve => setTimeout(() => resolve('Unknown'), 10000)) // 10 second timeout
                ]);
                console.log('Audio duration obtained:', duration);
            } catch (error) {
                console.warn('Failed to get audio duration:', error);
                duration = 'Unknown';
            }

            // Create final audio metadata
            console.log('Creating final audio metadata...');
            const finalAudio = {
                audioBlob: audioBlob,
                fileName: fileName,
                duration: duration,
                script: processedScript,
                metadata: {
                    generatedAt: new Date().toISOString(),
                    voice: options.voice || (usedMethod === 'gemini-tts' ? 'Kore' : 'system'),
                    method: usedMethod,
                    language: options.language || 'en-US',
                    source: 'transcription-audio'
                }
            };

            console.log('Setting current audio...');
            this.currentAudio = finalAudio;

            console.log('Updating progress to 100%...');
            this.updateProgress(100, 'Audio generation complete');

            console.log('Returning final audio object');
            return finalAudio;

        } catch (error) {
            console.error('Podcast generation error:', error);
            this.updateProgress(0, 'Podcast generation failed');
            throw error;
        } finally {
            this.isGenerating = false;
        }
    }

    async initializeAudioContext() {
        this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
        
        if (this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }

        // Create a destination for recording
        this.destination = this.audioContext.createMediaStreamDestination();
    }

    async generateSingleSpeakerAudio(script, options = {}) {
        try {
            console.log('Generating single speaker audio with Gemini TTS API...');

            // Ensure voice name is properly formatted for single speaker
            let voiceName = (options.voice || 'Kore');
            // Capitalize first letter for Gemini API
            voiceName = voiceName.charAt(0).toUpperCase() + voiceName.slice(1).toLowerCase();

            // Validate voice name against supported voices
            const validVoices = [
                'Kore', 'Puck', 'Charon', 'Fenrir', 'Aoede', 'Zephyr', 'Leda', 'Orus',
                'Callirrhoe', 'Autonoe', 'Enceladus', 'Iapetus', 'Umbriel', 'Algieba',
                'Despina', 'Erinome', 'Algenib', 'Rasalgethi', 'Laomedeia', 'Achernar',
                'Alnilam', 'Schedar', 'Gacrux', 'Pulcherrima', 'Achird', 'Zubenelgenubi',
                'Vindemiatrix', 'Sadachbia', 'Sadaltager', 'Sulafat'
            ];

            if (!validVoices.includes(voiceName)) {
                console.warn(`Voice "${voiceName}" not supported, using default "Kore"`);
                voiceName = 'Kore';
            }

            console.log(`Using single speaker voice: ${voiceName}`);

            // Create the proper single speaker request format
            const requestBody = {
                contents: [{
                    parts: [{
                        text: script
                    }]
                }],
                generationConfig: {
                    responseModalities: ["AUDIO"],
                    speechConfig: {
                        voiceConfig: {
                            prebuiltVoiceConfig: {
                                voiceName: voiceName
                            }
                        }
                    }
                }
            };

            console.log('Sending single speaker TTS request to Gemini API...');
            const response = await fetch(`${this.geminiAPI.baseUrl}/models/${this.geminiAPI.ttsModel}:generateContent?key=${this.geminiAPI.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error('Gemini TTS API Error:', errorText);
                throw new Error(`Single speaker TTS generation failed: ${response.status} ${response.statusText}`);
            }

            const data = await response.json();
            console.log('Received single speaker TTS response from Gemini API');

            // Check for audio data in the response
            const audioData = data.candidates?.[0]?.content?.parts?.[0]?.inlineData?.data;
            const mimeType = data.candidates?.[0]?.content?.parts?.[0]?.inlineData?.mimeType;

            if (!audioData) {
                console.error('No audio data in response:', JSON.stringify(data, null, 2));
                throw new Error('No audio data received from Gemini single speaker TTS API');
            }

            console.log('Audio data received, MIME type:', mimeType);

            // Convert base64 audio data to blob with proper MIME type
            const audioBytes = atob(audioData);
            const audioArray = new Uint8Array(audioBytes.length);
            for (let i = 0; i < audioBytes.length; i++) {
                audioArray[i] = audioBytes.charCodeAt(i);
            }

            // Use the MIME type from the response, fallback to common audio formats
            const detectedMimeType = mimeType || this.detectAudioFormat(audioArray) || 'audio/mpeg';
            const audioBlob = new Blob([audioArray], { type: detectedMimeType });
            console.log('Generated single speaker audio blob, size:', audioBlob.size, 'type:', detectedMimeType);

            return audioBlob;

        } catch (error) {
            console.error('Gemini single speaker TTS generation error:', error);
            throw new Error(`Failed to generate single speaker audio with Gemini TTS: ${error.message}`);
        }
    }

    detectAudioFormat(audioArray) {
        // Check for common audio format signatures
        const header = audioArray.slice(0, 12);

        // MP3 signature
        if (header[0] === 0xFF && (header[1] & 0xE0) === 0xE0) {
            return 'audio/mpeg';
        }

        // WAV signature
        if (header[0] === 0x52 && header[1] === 0x49 && header[2] === 0x46 && header[3] === 0x46) {
            return 'audio/wav';
        }

        // OGG signature
        if (header[0] === 0x4F && header[1] === 0x67 && header[2] === 0x67 && header[3] === 0x53) {
            return 'audio/ogg';
        }

        // WebM signature
        if (header[0] === 0x1A && header[1] === 0x45 && header[2] === 0xDF && header[3] === 0xA3) {
            return 'audio/webm';
        }

        // Default to MP3 if unknown
        console.warn('Unknown audio format, defaulting to audio/mpeg');
        return 'audio/mpeg';
    }

    async generateSpeechWithWebAPI(script, options = {}) {
        return new Promise(async (resolve, reject) => {
            try {
                console.log('Generating speech with Web Speech API...');

                if (!('speechSynthesis' in window)) {
                    throw new Error('Web Speech API not supported in this browser');
                }

                // Wait for voices to load
                await this.waitForVoices();

                // Create utterance
                const utterance = new SpeechSynthesisUtterance(script);

                // Configure voice settings
                const voices = speechSynthesis.getVoices();
                console.log('Available voices:', voices.map(v => v.name));

                const preferredVoice = voices.find(voice =>
                    voice.lang.startsWith('en') &&
                    (voice.name.includes('Google') || voice.name.includes('Microsoft') || voice.name.includes('Samantha'))
                ) || voices.find(voice => voice.lang.startsWith('en'));

                if (preferredVoice) {
                    utterance.voice = preferredVoice;
                    console.log('Using voice:', preferredVoice.name);
                }

                utterance.rate = options.rate || 0.9;
                utterance.pitch = options.pitch || 1.0;
                utterance.volume = options.volume || 1.0;

                // Set up audio recording using MediaRecorder
                try {
                    const stream = await navigator.mediaDevices.getUserMedia({
                        audio: {
                            echoCancellation: false,
                            noiseSuppression: false,
                            autoGainControl: false,
                            sampleRate: 44100
                        }
                    });

                    const mediaRecorder = new MediaRecorder(stream, {
                        mimeType: 'audio/webm;codecs=opus'
                    });

                    const audioChunks = [];
                    let recordingStarted = false;

                    mediaRecorder.ondataavailable = (event) => {
                        if (event.data.size > 0) {
                            audioChunks.push(event.data);
                        }
                    };

                    mediaRecorder.onstop = () => {
                        stream.getTracks().forEach(track => track.stop());
                        const audioBlob = new Blob(audioChunks, { type: 'audio/webm' });
                        console.log('Generated Web Speech audio blob, size:', audioBlob.size);

                        if (audioBlob.size > 0) {
                            resolve(audioBlob);
                        } else {
                            reject(new Error('Generated audio blob is empty'));
                        }
                    };

                    utterance.onstart = () => {
                        console.log('Starting Web Speech synthesis...');
                        if (!recordingStarted) {
                            mediaRecorder.start();
                            recordingStarted = true;
                        }
                    };

                    utterance.onend = () => {
                        console.log('Web Speech synthesis completed');
                        setTimeout(() => {
                            if (mediaRecorder.state === 'recording') {
                                mediaRecorder.stop();
                            }
                        }, 1000); // Longer delay to capture all audio
                    };

                    utterance.onerror = (event) => {
                        console.error('Speech synthesis error:', event);
                        if (mediaRecorder.state === 'recording') {
                            mediaRecorder.stop();
                        }
                        reject(new Error(`Speech synthesis failed: ${event.error}`));
                    };

                    // Start speech synthesis
                    speechSynthesis.speak(utterance);

                } catch (mediaError) {
                    console.warn('MediaRecorder setup failed, using alternative method:', mediaError);
                    // Fallback: create a simple audio blob without recording
                    this.createSimpleAudioBlob(script, utterance).then(resolve).catch(reject);
                }

            } catch (error) {
                console.error('Web Speech API generation failed:', error);
                reject(error);
            }
        });
    }

    async waitForVoices() {
        return new Promise((resolve) => {
            const voices = speechSynthesis.getVoices();
            if (voices.length > 0) {
                resolve(voices);
            } else {
                speechSynthesis.onvoiceschanged = () => {
                    resolve(speechSynthesis.getVoices());
                };
            }
        });
    }

    async createSimpleAudioBlob(script, utterance) {
        return new Promise((resolve, reject) => {
            // Create a simple placeholder audio blob for testing
            const sampleRate = 44100;
            const duration = Math.max(script.length / 10, 5); // Estimate duration
            const samples = sampleRate * duration;
            const buffer = new ArrayBuffer(44 + samples * 2);
            const view = new DataView(buffer);

            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };

            writeString(0, 'RIFF');
            view.setUint32(4, 36 + samples * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, 1, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * 2, true);
            view.setUint16(32, 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, samples * 2, true);

            // Generate simple tone as placeholder
            for (let i = 0; i < samples; i++) {
                const sample = Math.sin(2 * Math.PI * 440 * i / sampleRate) * 0.1;
                view.setInt16(44 + i * 2, sample * 32767, true);
            }

            const audioBlob = new Blob([buffer], { type: 'audio/wav' });
            console.log('Created simple audio blob as fallback, size:', audioBlob.size);

            // Still try to use speech synthesis for the actual audio
            utterance.onend = () => {
                console.log('Speech synthesis completed (simple blob fallback)');
            };

            speechSynthesis.speak(utterance);
            resolve(audioBlob);
        });
    }

    processTranscriptionForSingleSpeaker(script, options = {}) {
        console.log('Processing transcription for single speaker audio...');

        // Clean and format the transcription for single speaker narration
        let processed = script
            // Remove markdown formatting
            .replace(/[#*_`]/g, '')
            // Convert speaker labels to narrative format for single speaker
            .replace(/Speaker (\d+):/g, (match, num) => {
                const speakerNames = ['the first speaker', 'the second speaker', 'the third speaker'];
                return `${speakerNames[parseInt(num) - 1] || `speaker ${num}`} said:`;
            })
            // Add natural pauses
            .replace(/\./g, '. ')
            .replace(/,/g, ', ')
            .replace(/\?/g, '? ')
            .replace(/!/g, '! ')
            // Add narrative transitions
            .replace(/\n\s*([a-z])/gi, (match, letter) => `. ${letter.toUpperCase()}`)
            // Add pauses for paragraph breaks
            .replace(/\n\n/g, ' ... ')
            .replace(/\n/g, ' ')
            // Clean up multiple spaces
            .replace(/\s+/g, ' ')
            .trim();

        // Ensure it doesn't exceed reasonable length for TTS
        if (processed.length > 5000) {
            console.log('Script too long, truncating...');
            processed = processed.substring(0, 4800) + '... and that concludes our summary.';
        }

        // Add natural speech instructions for single speaker narration
        const style = options.style || 'conversational and clear';
        processed = `Please read the following content in a ${style} manner: ${processed}`;

        console.log('Processed transcription for single speaker, final length:', processed.length);
        return processed;
    }

    processTranscriptionScript(script, options = {}) {
        // Legacy method - redirect to single speaker processing
        return this.processTranscriptionForSingleSpeaker(script, options);
    }

    processScript(script) {
        // Legacy method - redirect to transcription processing
        return this.processTranscriptionScript(script);
    }

    async setupAudioRecording() {
        // Create a MediaRecorder to capture the TTS output
        const stream = this.destination.stream;
        
        this.mediaRecorder = new MediaRecorder(stream, {
            mimeType: this.getSupportedMimeType()
        });

        this.audioChunks = [];

        this.mediaRecorder.ondataavailable = (event) => {
            if (event.data.size > 0) {
                this.audioChunks.push(event.data);
            }
        };

        return new Promise((resolve) => {
            this.mediaRecorder.onstart = () => resolve();
            this.mediaRecorder.start();
        });
    }

    async generateSpeech(script, options = {}) {
        return new Promise((resolve, reject) => {
            try {
                // Select the best available voice
                const selectedVoice = this.selectVoice(options.voice);
                
                // Create speech synthesis utterance
                const utterance = new SpeechSynthesisUtterance(script);
                
                // Configure voice settings
                utterance.voice = selectedVoice;
                utterance.rate = options.speed || 0.9; // Slightly slower for better comprehension
                utterance.pitch = options.pitch || 1.0;
                utterance.volume = 1.0;

                // Handle speech events
                utterance.onstart = () => {
                    console.log('Speech synthesis started');
                };

                utterance.onend = () => {
                    console.log('Speech synthesis completed');
                    
                    // Stop recording and get the audio blob
                    this.mediaRecorder.stop();
                    
                    this.mediaRecorder.onstop = () => {
                        const audioBlob = new Blob(this.audioChunks, { 
                            type: this.getSupportedMimeType() 
                        });
                        resolve(audioBlob);
                    };
                };

                utterance.onerror = (event) => {
                    console.error('Speech synthesis error:', event.error);
                    reject(new Error(`Speech synthesis failed: ${event.error}`));
                };

                // Connect speech synthesis to our audio context for recording
                // Note: This is a limitation - we can't directly capture speechSynthesis output
                // In a real implementation, you'd use a cloud TTS service
                
                // Start speech synthesis
                this.speechSynthesis.speak(utterance);

            } catch (error) {
                reject(error);
            }
        });
    }

    selectVoice(preferredVoice) {
        if (preferredVoice) {
            const voice = this.voices.find(v => 
                v.name.toLowerCase().includes(preferredVoice.toLowerCase())
            );
            if (voice) return voice;
        }

        // Return the best available voice
        if (this.preferredVoices.length > 0) {
            return this.preferredVoices[0];
        }

        // Fallback to first available voice
        return this.voices[0] || null;
    }

    async createFinalPodcast(audioBlob, options = {}) {
        // For now, return the audio blob as-is
        // In a more advanced implementation, you could:
        // - Add intro/outro music
        // - Apply audio effects
        // - Normalize audio levels
        // - Add background music
        
        return audioBlob;
    }

    async getAudioDuration(audioBlob) {
        return new Promise((resolve, reject) => {
            const audio = new Audio();
            let resolved = false;

            // Set up timeout to prevent hanging
            const timeout = setTimeout(() => {
                if (!resolved) {
                    resolved = true;
                    console.warn('Audio duration detection timed out, using estimated duration');
                    // Estimate duration based on file size (rough approximation)
                    const estimatedDuration = Math.max(audioBlob.size / 32000, 10); // ~32KB per second
                    resolve(`${Math.floor(estimatedDuration / 60)}:${String(Math.floor(estimatedDuration % 60)).padStart(2, '0')}`);
                }
            }, 5000); // 5 second timeout

            audio.onloadedmetadata = () => {
                if (!resolved) {
                    resolved = true;
                    clearTimeout(timeout);
                    const duration = audio.duration;
                    const minutes = Math.floor(duration / 60);
                    const seconds = Math.floor(duration % 60);
                    resolve(`${minutes}:${String(seconds).padStart(2, '0')}`);
                    URL.revokeObjectURL(audio.src);
                }
            };

            audio.onerror = () => {
                if (!resolved) {
                    resolved = true;
                    clearTimeout(timeout);
                    console.warn('Failed to load audio metadata, using estimated duration');
                    const estimatedDuration = Math.max(audioBlob.size / 32000, 10);
                    resolve(`${Math.floor(estimatedDuration / 60)}:${String(Math.floor(estimatedDuration % 60)).padStart(2, '0')}`);
                    URL.revokeObjectURL(audio.src);
                }
            };

            try {
                audio.src = URL.createObjectURL(audioBlob);
            } catch (error) {
                if (!resolved) {
                    resolved = true;
                    clearTimeout(timeout);
                    console.error('Error creating audio URL:', error);
                    resolve('Unknown');
                }
            }
        });
    }

    getSupportedMimeType() {
        const types = [
            'audio/webm;codecs=opus',
            'audio/webm',
            'audio/mp4',
            'audio/ogg;codecs=opus'
        ];
        
        for (const type of types) {
            if (MediaRecorder.isTypeSupported(type)) {
                return type;
            }
        }
        
        return 'audio/webm'; // Fallback
    }

    // Alternative method using Web Audio API for better control
    async generatePodcastWithWebAudio(script, options = {}) {
        try {
            this.updateProgress(10, 'Initializing advanced audio generation...');

            // This would be used with a cloud TTS service
            // For demonstration, we'll create a simple tone-based audio
            const audioBuffer = await this.createSyntheticAudio(script.length);
            
            this.updateProgress(50, 'Processing audio...');

            // Convert AudioBuffer to Blob
            const audioBlob = await this.audioBufferToBlob(audioBuffer);

            this.updateProgress(100, 'Audio generation complete');

            return audioBlob;

        } catch (error) {
            console.error('Web Audio generation error:', error);
            throw error;
        }
    }

    async createSyntheticAudio(textLength) {
        // Create a simple synthetic audio for demonstration
        // In a real implementation, this would call a cloud TTS service
        
        const duration = Math.max(textLength / 150 * 60, 10); // Rough estimate: 150 words per minute
        const sampleRate = 44100;
        const length = sampleRate * duration;
        
        const audioBuffer = this.audioContext.createBuffer(1, length, sampleRate);
        const channelData = audioBuffer.getChannelData(0);
        
        // Generate a simple tone pattern to represent speech
        for (let i = 0; i < length; i++) {
            const time = i / sampleRate;
            const frequency = 200 + Math.sin(time * 0.5) * 50; // Varying frequency
            const amplitude = 0.1 * Math.sin(time * 2 * Math.PI * 0.1); // Amplitude modulation
            channelData[i] = amplitude * Math.sin(2 * Math.PI * frequency * time);
        }
        
        return audioBuffer;
    }

    async audioBufferToBlob(audioBuffer) {
        return new Promise((resolve) => {
            const numberOfChannels = audioBuffer.numberOfChannels;
            const length = audioBuffer.length;
            const sampleRate = audioBuffer.sampleRate;
            
            // Create WAV file
            const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
            const view = new DataView(arrayBuffer);
            
            // WAV header
            const writeString = (offset, string) => {
                for (let i = 0; i < string.length; i++) {
                    view.setUint8(offset + i, string.charCodeAt(i));
                }
            };
            
            writeString(0, 'RIFF');
            view.setUint32(4, 36 + length * numberOfChannels * 2, true);
            writeString(8, 'WAVE');
            writeString(12, 'fmt ');
            view.setUint32(16, 16, true);
            view.setUint16(20, 1, true);
            view.setUint16(22, numberOfChannels, true);
            view.setUint32(24, sampleRate, true);
            view.setUint32(28, sampleRate * numberOfChannels * 2, true);
            view.setUint16(32, numberOfChannels * 2, true);
            view.setUint16(34, 16, true);
            writeString(36, 'data');
            view.setUint32(40, length * numberOfChannels * 2, true);
            
            // Convert audio data
            let offset = 44;
            for (let i = 0; i < length; i++) {
                for (let channel = 0; channel < numberOfChannels; channel++) {
                    const sample = Math.max(-1, Math.min(1, audioBuffer.getChannelData(channel)[i]));
                    view.setInt16(offset, sample * 0x7FFF, true);
                    offset += 2;
                }
            }
            
            resolve(new Blob([arrayBuffer], { type: 'audio/wav' }));
        });
    }

    createDownloadLink(audioBlob, filename = null) {
        const url = URL.createObjectURL(audioBlob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename || `podcast_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.webm`;
        a.textContent = 'Download Podcast';
        a.className = 'btn btn-primary';
        
        return a;
    }

    getAvailableVoices() {
        return this.availableVoices.map(voice => ({
            name: voice,
            description: this.getVoiceDescription(voice),
            quality: 'Premium',
            provider: 'Gemini TTS'
        }));
    }

    getVoiceDescription(voiceName) {
        const descriptions = {
            'zephyr': 'Bright',
            'puck': 'Upbeat',
            'charon': 'Informative',
            'kore': 'Firm',
            'fenrir': 'Excitable',
            'leda': 'Youthful',
            'orus': 'Firm',
            'aoede': 'Breezy',
            'callirrhoe': 'Easy-going',
            'autonoe': 'Bright',
            'enceladus': 'Breathy',
            'iapetus': 'Clear',
            'umbriel': 'Easy-going',
            'algieba': 'Smooth',
            'despina': 'Smooth',
            'erinome': 'Clear',
            'algenib': 'Gravelly',
            'rasalgethi': 'Informative',
            'laomedeia': 'Upbeat',
            'achernar': 'Soft',
            'alnilam': 'Firm',
            'schedar': 'Even',
            'gacrux': 'Mature',
            'pulcherrima': 'Forward',
            'achird': 'Friendly',
            'zubenelgenubi': 'Casual',
            'vindemiatrix': 'Gentle',
            'sadachbia': 'Lively',
            'sadaltager': 'Knowledgeable',
            'sulafat': 'Warm'
        };
        return descriptions[voiceName.toLowerCase()] || 'Natural';
    }

    updateProgress(percentage, message) {
        if (window.updateProgress) {
            window.updateProgress(percentage, message);
        }
    }

    async saveAudioFile(audioBlob, options = {}) {
        try {
            // Generate a unique filename
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const fileName = `audio_${timestamp}.wav`;

            // Create a download link and trigger download to save in project directory
            const url = URL.createObjectURL(audioBlob);
            const link = document.createElement('a');
            link.href = url;
            link.download = fileName;

            // Store the file reference for cleanup
            if (!this.generatedFiles) {
                this.generatedFiles = [];
            }
            this.generatedFiles.push({ fileName, url, blob: audioBlob });

            console.log(`Audio saved as: ${fileName}`);
            return fileName;

        } catch (error) {
            console.error('Error saving audio file:', error);
            throw new Error(`Failed to save audio file: ${error.message}`);
        }
    }

    createDownloadLink(audioBlob, fileName = null) {
        if (!fileName) {
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            fileName = `audio_${timestamp}.wav`;
        }

        const url = URL.createObjectURL(audioBlob);
        const link = document.createElement('a');
        link.href = url;
        link.download = fileName;
        link.style.display = 'none';

        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);

        // Clean up the URL after a delay
        setTimeout(() => {
            URL.revokeObjectURL(url);
        }, 1000);

        return link;
    }

    clearGeneratedFiles() {
        if (this.generatedFiles) {
            this.generatedFiles.forEach(file => {
                if (file.url) {
                    URL.revokeObjectURL(file.url);
                }
            });
            this.generatedFiles = [];
            console.log('Cleared all generated audio files');
        }

        // Clear current audio reference
        this.currentAudio = null;
        this.currentPodcast = null; // Keep for backward compatibility
    }

    cleanup() {
        this.clearGeneratedFiles();
        this.isGenerating = false;

        if (this.audioContext) {
            this.audioContext.close();
            this.audioContext = null;
        }
    }

    stop() {
        this.cleanup();
    }
}

// Export for use in other modules
window.PodcastGenerator = PodcastGenerator;
