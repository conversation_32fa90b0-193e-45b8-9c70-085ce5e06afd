{"name": "portfinder", "description": "A simple tool to find an open port on the current machine", "version": "1.0.37", "author": "<PERSON> <<EMAIL>>, <PERSON> <<EMAIL>>", "repository": {"type": "git", "url": "**************:http-party/node-portfinder.git"}, "keywords": ["http", "ports", "utilities"], "files": ["lib"], "dependencies": {"async": "^3.2.6", "debug": "^4.3.6"}, "devDependencies": {"jest": "^29.7.0"}, "main": "./lib/portfinder", "types": "./lib/portfinder.d.ts", "scripts": {"test": "jest --runInBand"}, "engines": {"node": ">= 10.12"}, "license": "MIT"}