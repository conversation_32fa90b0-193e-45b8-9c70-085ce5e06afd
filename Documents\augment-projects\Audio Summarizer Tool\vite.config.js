import { defineConfig } from 'vite'

export default defineConfig({
  // Development server configuration
  server: {
    port: 3000,
    open: true,
    cors: true,
    // Enable HTTPS for media API access
    https: false, // Set to true if you need HTTPS for testing
    host: true, // Allow external connections
  },

  // Build configuration
  build: {
    outDir: 'dist',
    assetsDir: 'assets',
    sourcemap: true,
    rollupOptions: {
      input: {
        main: './index.html',
        test: './test-audio.html'
      }
    }
  },

  // Base path for deployment
  base: './',

  // Asset handling
  assetsInclude: ['**/*.wav', '**/*.mp3', '**/*.webm'],

  // Plugin configuration
  plugins: [],

  // CSS configuration
  css: {
    postcss: false, // Disable PostCSS processing
  },

  // Optimization
  optimizeDeps: {
    include: []
  }
})
