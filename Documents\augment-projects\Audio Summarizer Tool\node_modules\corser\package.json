{"name": "corser", "version": "2.0.1", "description": "A highly configurable, middleware compatible implementation of CORS.", "keywords": ["cors", "cross-origin resource sharing", "connect", "express", "middleware"], "bugs": "https://github.com/agrueneberg/Corser/issues", "license": "MIT", "author": "<PERSON> <<EMAIL>>", "main": "./lib/corser.js", "repository": {"type": "git", "url": "https://github.com/agrueneberg/Corser.git"}, "scripts": {"test": "./node_modules/.bin/mocha"}, "devDependencies": {"mocha": "1.3.x", "expect.js": "0.1.x"}, "engines": {"node": ">= 0.4.0"}}