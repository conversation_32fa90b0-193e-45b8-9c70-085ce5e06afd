/**
 * Main Application Controller
 * Coordinates all modules and handles user interactions
 */

class AudioSummarizerApp {
    constructor() {
        this.audioProcessor = new AudioProcessor();
        this.geminiAPI = new GeminiAPI();
        this.speakerDetection = new SpeakerDetection();
        this.audioGenerator = new AudioGenerator(this.geminiAPI);
        
        this.currentRecording = null;
        this.currentTranscription = null;
        this.currentSummary = null;
        this.currentSpeakers = null;
        this.currentPodcast = null;
        
        this.recordingTimer = null;
        this.recordingStartTime = null;
        
        this.initializeApp();
    }

    initializeApp() {
        this.setupEventListeners();
        this.checkApiConfiguration();
        this.checkAudioCapabilities();
        this.updateUI();
    }

    checkAudioCapabilities() {
        // Check if system audio is available
        if (!navigator.mediaDevices?.getDisplayMedia) {
            const systemAudioCheckbox = document.getElementById('systemAudioInput');
            if (systemAudioCheckbox) {
                systemAudioCheckbox.disabled = true;
                systemAudioCheckbox.parentElement.style.opacity = '0.5';
                systemAudioCheckbox.parentElement.title = 'System audio not supported in this browser';
            }
            this.showToast('System audio capture not supported in this browser. Use Chrome, Edge, or Firefox for best results.', 'warning');
        }

        // Check for HTTPS requirement
        if (location.protocol !== 'https:' && location.hostname !== 'localhost') {
            this.showToast('HTTPS is required for audio recording. Some features may not work.', 'warning');
        }
    }

    async runAudioDiagnostics() {
        try {
            this.showLoading('Running audio diagnostics...');

            const results = {
                browser: this.getBrowserInfo(),
                compatibility: this.audioProcessor.checkBrowserCompatibility(),
                devices: await this.audioProcessor.checkAudioDeviceStatus(),
                permissions: await this.checkPermissions(),
                timestamp: new Date().toISOString()
            };

            console.log('Audio Diagnostics Results:', results);

            // Display results in a formatted way
            this.displayDiagnosticResults(results);

            this.hideLoading();

        } catch (error) {
            this.hideLoading();
            this.showToast(`Diagnostics failed: ${error.message}`, 'error');
            console.error('Diagnostics error:', error);
        }
    }

    getBrowserInfo() {
        const ua = navigator.userAgent;
        let browser = 'Unknown';

        if (ua.includes('Chrome') && !ua.includes('Edg')) browser = 'Chrome';
        else if (ua.includes('Firefox')) browser = 'Firefox';
        else if (ua.includes('Safari') && !ua.includes('Chrome')) browser = 'Safari';
        else if (ua.includes('Edg')) browser = 'Edge';

        return {
            name: browser,
            userAgent: ua,
            platform: navigator.platform,
            language: navigator.language
        };
    }

    async checkPermissions() {
        const permissions = {};

        try {
            // Check microphone permission
            const micPermission = await navigator.permissions.query({ name: 'microphone' });
            permissions.microphone = micPermission.state;
        } catch (error) {
            permissions.microphone = 'unknown';
        }

        try {
            // Check camera permission (needed for screen share)
            const cameraPermission = await navigator.permissions.query({ name: 'camera' });
            permissions.camera = cameraPermission.state;
        } catch (error) {
            permissions.camera = 'unknown';
        }

        return permissions;
    }

    displayDiagnosticResults(results) {
        const diagnosticInfo = `
            <div class="diagnostic-results">
                <h3>Audio Diagnostics Report</h3>

                <div class="diagnostic-section">
                    <h4>Browser Information</h4>
                    <p><strong>Browser:</strong> ${results.browser.name}</p>
                    <p><strong>Platform:</strong> ${results.browser.platform}</p>
                    <p><strong>Language:</strong> ${results.browser.language}</p>
                </div>

                <div class="diagnostic-section">
                    <h4>Audio Devices</h4>
                    <p><strong>Audio Inputs:</strong> ${results.devices?.audioInputs || 'Unknown'}</p>
                    <p><strong>Audio Outputs:</strong> ${results.devices?.audioOutputs || 'Unknown'}</p>
                    <p><strong>Audio Context:</strong> ${results.devices?.contextState || 'Unknown'}</p>
                </div>

                <div class="diagnostic-section">
                    <h4>Permissions</h4>
                    <p><strong>Microphone:</strong> ${results.permissions.microphone}</p>
                    <p><strong>Camera:</strong> ${results.permissions.camera}</p>
                </div>

                <div class="diagnostic-section">
                    <h4>Compatibility</h4>
                    <p><strong>Overall:</strong> ${results.compatibility ? '✅ Compatible' : '❌ Issues detected'}</p>
                    <p><strong>Protocol:</strong> ${location.protocol}</p>
                    <p><strong>Host:</strong> ${location.hostname}</p>
                </div>

                <div class="diagnostic-actions">
                    <button onclick="app.copyDiagnostics()" class="btn btn-secondary">Copy to Clipboard</button>
                    <button onclick="app.clearDiagnostics()" class="btn btn-secondary">Close</button>
                </div>
            </div>
        `;

        // Show in transcription tab for now
        document.getElementById('transcriptionContent').innerHTML = diagnosticInfo;
        this.switchTab('transcription');

        // Store results for copying
        this.lastDiagnosticResults = results;
    }

    copyDiagnostics() {
        if (this.lastDiagnosticResults) {
            const text = JSON.stringify(this.lastDiagnosticResults, null, 2);
            navigator.clipboard.writeText(text).then(() => {
                this.showToast('Diagnostic results copied to clipboard', 'success');
            }).catch(() => {
                this.showToast('Failed to copy to clipboard', 'error');
            });
        }
    }

    clearDiagnostics() {
        document.getElementById('transcriptionContent').innerHTML =
            '<p class="placeholder-text">Transcription will appear here after processing...</p>';
    }

    setupEventListeners() {
        // Settings Panel
        document.getElementById('settingsBtn').addEventListener('click', () => {
            this.toggleSettingsPanel();
        });

        document.getElementById('closeSettings').addEventListener('click', () => {
            this.hideSettingsPanel();
        });

        // API Configuration - Settings Panel
        document.getElementById('saveApiKey').addEventListener('click', () => {
            this.saveApiKey();
        });

        // API Configuration - Fallback Panel
        document.getElementById('saveApiKeyFallback').addEventListener('click', () => {
            this.saveApiKeyFallback();
        });

        // Close settings panel when clicking outside
        document.addEventListener('click', (e) => {
            const settingsPanel = document.getElementById('settingsPanel');
            const settingsBtn = document.getElementById('settingsBtn');

            if (settingsPanel.classList.contains('active') &&
                !settingsPanel.contains(e.target) &&
                !settingsBtn.contains(e.target)) {
                this.hideSettingsPanel();
            }
        });

        // Recording Controls
        document.getElementById('startRecording').addEventListener('click', () => {
            this.startRecording();
        });

        document.getElementById('stopRecording').addEventListener('click', () => {
            this.stopRecording();
        });

        document.getElementById('pauseRecording').addEventListener('click', () => {
            this.pauseRecording();
        });

        // Processing
        document.getElementById('processAudio').addEventListener('click', () => {
            this.processAudio();
        });

        // Diagnostics
        document.getElementById('runDiagnostics').addEventListener('click', () => {
            this.runAudioDiagnostics();
        });

        // Results
        document.getElementById('exportResults').addEventListener('click', () => {
            this.exportResults();
        });

        document.getElementById('clearResults').addEventListener('click', () => {
            this.clearResults();
        });

        // Audio Generation
        document.getElementById('generateAudioBtn').addEventListener('click', () => {
            this.generateAudio();
        });

        // Tab Navigation
        document.querySelectorAll('.tab-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                this.switchTab(e.target.dataset.tab);
            });
        });

        // Keyboard shortcuts
        document.addEventListener('keydown', (e) => {
            this.handleKeyboardShortcuts(e);
        });

        // Window events
        window.addEventListener('beforeunload', (e) => {
            if (this.audioProcessor.isRecording) {
                e.preventDefault();
                e.returnValue = 'Recording in progress. Are you sure you want to leave?';
            }
        });
    }

    checkApiConfiguration() {
        if (!this.geminiAPI.isConfigured()) {
            this.showApiConfigPanel();
            this.showToast('Please configure your Gemini API key to use AI features', 'warning');
        }
    }

    toggleSettingsPanel() {
        const panel = document.getElementById('settingsPanel');
        panel.classList.toggle('active');

        // Sync API key between settings and fallback inputs
        const settingsInput = document.getElementById('geminiApiKey');
        const fallbackInput = document.getElementById('geminiApiKeyFallback');
        if (settingsInput.value && !fallbackInput.value) {
            fallbackInput.value = settingsInput.value;
        } else if (fallbackInput.value && !settingsInput.value) {
            settingsInput.value = fallbackInput.value;
        }
    }

    hideSettingsPanel() {
        document.getElementById('settingsPanel').classList.remove('active');
    }

    toggleApiConfigPanel() {
        const panel = document.getElementById('apiConfigPanel');
        panel.classList.toggle('active');
    }

    showApiConfigPanel() {
        document.getElementById('apiConfigPanel').classList.add('active');
    }

    hideApiConfigPanel() {
        document.getElementById('apiConfigPanel').classList.remove('active');
    }

    async saveApiKey() {
        const apiKeyInput = document.getElementById('geminiApiKey');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }

        try {
            this.showLoading('Validating API key...');

            this.geminiAPI.saveApiKey(apiKey);
            await this.geminiAPI.validateApiKey();

            // Sync with fallback input
            document.getElementById('geminiApiKeyFallback').value = apiKey;

            this.hideLoading();
            this.hideSettingsPanel();
            this.showToast('API key validated and saved successfully', 'success');
            
            // Clear the input for security
            apiKeyInput.value = '';
            
        } catch (error) {
            this.hideLoading();
            this.showToast(`API key validation failed: ${error.message}`, 'error');
        }
    }

    async saveApiKeyFallback() {
        const apiKeyInput = document.getElementById('geminiApiKeyFallback');
        const apiKey = apiKeyInput.value.trim();

        if (!apiKey) {
            this.showToast('Please enter a valid API key', 'error');
            return;
        }

        try {
            this.showLoading('Validating API key...');

            this.geminiAPI.saveApiKey(apiKey);
            await this.geminiAPI.validateApiKey();

            // Sync with settings input
            document.getElementById('geminiApiKey').value = apiKey;

            this.hideLoading();
            this.hideApiConfigPanel();
            this.showToast('API key validated and saved successfully', 'success');

            // Clear the input for security
            apiKeyInput.value = '';

        } catch (error) {
            this.hideLoading();
            this.showToast(`API key validation failed: ${error.message}`, 'error');
        }
    }

    async startRecording() {
        try {
            const success = await this.audioProcessor.startRecording();
            
            if (success) {
                this.updateRecordingUI(true);
                this.startRecordingTimer();
                this.clearResults();
            }
            
        } catch (error) {
            this.showToast(`Failed to start recording: ${error.message}`, 'error');
        }
    }

    stopRecording() {
        console.log('Stopping recording...');
        this.audioProcessor.stopRecording();
        this.updateRecordingUI(false);
        this.stopRecordingTimer();

        // Wait a moment for the recording to be processed
        setTimeout(() => {
            this.currentRecording = this.audioProcessor.getRecordedAudio();
            console.log('Retrieved recording after stop:', !!this.currentRecording);

            if (this.currentRecording) {
                document.getElementById('processAudio').disabled = false;
                this.showToast('Recording saved. Ready for processing.', 'success');
                console.log('Recording ready, size:', this.currentRecording.size);
            } else {
                this.showToast('Recording failed to save properly', 'error');
                console.error('No recording available after stop');
            }
        }, 100); // Small delay to ensure recording is processed
    }

    pauseRecording() {
        if (this.audioProcessor.isPaused) {
            this.audioProcessor.resumeRecording();
            this.startRecordingTimer();
        } else {
            this.audioProcessor.pauseRecording();
            this.stopRecordingTimer();
        }
        
        this.updatePauseButton();
    }

    async processAudio() {
        console.log('processAudio called');

        // Get the current recording from the audio processor
        this.currentRecording = this.audioProcessor.getRecordedAudio();

        if (!this.currentRecording) {
            console.error('No recording found');
            this.showToast('No recording available to process. Please record audio first.', 'error');
            return;
        }

        console.log('Found recording, size:', this.currentRecording.size);

        if (!this.geminiAPI.isConfigured()) {
            this.showToast('Please configure your Gemini API key first', 'error');
            this.showApiConfigPanel();
            return;
        }

        try {
            this.showLoading('Processing audio with AI...');

            const speakerIdentification = document.getElementById('speakerIdentification').checked;
            const realTimeProcessing = document.getElementById('realTimeProcessing').checked;

            console.log('Starting transcription...');
            // Step 1: Transcribe audio
            this.updateProgress(10, 'Transcribing audio...');
            const transcriptionResult = await this.geminiAPI.transcribeAudio(
                this.currentRecording,
                { speakerIdentification }
            );

            console.log('Transcription completed');
            this.currentTranscription = transcriptionResult;
            this.displayTranscription(transcriptionResult);

            // Step 2: Analyze speakers if enabled
            if (speakerIdentification) {
                console.log('Starting speaker analysis...');
                this.updateProgress(40, 'Analyzing speakers...');
                try {
                    const speakerResults = await this.speakerDetection.analyzeSpeakers(
                        this.currentRecording,
                        transcriptionResult.transcription
                    );

                    console.log('Speaker analysis completed');
                    this.currentSpeakers = speakerResults;
                    this.displaySpeakers(speakerResults);
                } catch (speakerError) {
                    console.warn('Speaker analysis failed, continuing without it:', speakerError);
                    this.showToast('Speaker analysis failed, continuing with transcription only', 'warning');
                }
            }

            // Step 3: Generate summary
            console.log('Starting summarization...');
            this.updateProgress(70, 'Generating summary...');
            const summaryResult = await this.geminiAPI.summarizeText(
                transcriptionResult.transcription,
                { speakerIdentification }
            );

            console.log('Summarization completed');
            this.currentSummary = summaryResult;
            this.displaySummary(summaryResult);

            this.updateProgress(100, 'Processing complete');
            this.hideLoading();

            document.getElementById('exportResults').disabled = false;
            this.showToast('Audio processing completed successfully', 'success');

        } catch (error) {
            this.hideLoading();
            this.showToast(`Processing failed: ${error.message}`, 'error');
            console.error('Processing error:', error);
        }
    }

    async generateAudio() {
        if (!this.currentTranscription) {
            this.showToast('Please process audio and generate a transcription first', 'error');
            return;
        }

        try {
            this.showLoading('Generating single speaker audio...');

            // Use the transcribed text directly for single speaker audio generation
            this.updateProgress(20, 'Preparing transcription for single speaker audio...');

            // Use the transcription directly without complex script generation
            const transcriptionText = this.currentTranscription.transcription || this.currentTranscription;

            // Convert transcribed text to audio using Gemini single speaker TTS
            this.updateProgress(50, 'Converting transcription to single speaker audio...');
            const audioResult = await this.audioGenerator.generateAudio(
                transcriptionText,
                {
                    voice: 'Kore', // Use a valid Gemini TTS voice
                    language: 'en-US',
                    style: 'conversational and clear'
                }
            );

            this.currentAudio = audioResult;
            this.currentPodcast = audioResult; // Keep for backward compatibility
            this.displayAudio(audioResult);

            this.updateProgress(100, 'Audio generation complete');
            this.hideLoading();

            this.showToast('Single speaker audio generated successfully', 'success');
            this.switchTab('podcast');

        } catch (error) {
            this.hideLoading();
            this.showToast(`Audio generation failed: ${error.message}`, 'error');
            console.error('Audio generation error:', error);
        }
    }

    // Keep legacy method for backward compatibility
    async generatePodcast() {
        return this.generateAudio();
    }

    displayTranscription(result) {
        const content = document.getElementById('transcriptionContent');
        content.innerHTML = `
            <div class="transcription-result">
                <div class="transcription-meta">
                    <span class="confidence">Confidence: ${(result.confidence * 100).toFixed(1)}%</span>
                    <span class="language">Language: ${result.language}</span>
                </div>
                <div class="transcription-text">
                    ${this.formatTranscriptionText(result.transcription)}
                </div>
            </div>
        `;
    }

    displaySummary(result) {
        const content = document.getElementById('summaryContent');
        content.innerHTML = `
            <div class="summary-result">
                <div class="summary-meta">
                    <span class="word-count">Original: ${result.wordCount} words</span>
                    <span class="ratio">Compression: ${(result.summaryRatio * 100).toFixed(1)}%</span>
                </div>
                <div class="summary-text">
                    ${this.formatSummaryText(result.summary)}
                </div>
                ${result.keyPoints.length > 0 ? `
                    <div class="key-points">
                        <h4>Key Points:</h4>
                        <ul>
                            ${result.keyPoints.map(point => `<li>${point}</li>`).join('')}
                        </ul>
                    </div>
                ` : ''}
            </div>
        `;
    }

    displaySpeakers(results) {
        const content = document.getElementById('speakersContent');
        content.innerHTML = this.speakerDetection.formatSpeakerResults();
    }

    displayAudio(result) {
        const content = document.getElementById('podcastContent');

        // Create audio URL and store reference for cleanup
        const audioUrl = URL.createObjectURL(result.audioBlob);

        // Store URL for cleanup
        if (!this.audioUrls) {
            this.audioUrls = [];
        }
        this.audioUrls.push(audioUrl);

        // Determine the method used for generation
        const methodDisplay = result.metadata.method === 'gemini-tts' ?
            '<span class="method-badge gemini">Generated with Gemini Single Speaker TTS</span>' :
            '<span class="method-badge web-api">Generated with Web Speech API</span>';

        content.innerHTML = `
            <div class="audio-result">
                <div class="audio-meta">
                    <div class="meta-row">
                        <span class="duration">Duration: ${result.duration || 'Unknown'}</span>
                        <span class="generated-at">Generated: ${new Date(result.metadata.generatedAt).toLocaleString()}</span>
                    </div>
                    <div class="meta-row">
                        ${methodDisplay}
                        <span class="voice-info">Voice: ${result.metadata.voice}</span>
                    </div>
                    <div class="meta-row">
                        <span class="file-info">Type: ${result.audioBlob.type}</span>
                        <span class="file-info">Size: ${this.formatFileSize(result.audioBlob.size)}</span>
                    </div>
                    ${result.fileName ? `<div class="meta-row"><span class="file-info">File: ${result.fileName}</span></div>` : ''}
                </div>
                <div class="audio-controls">
                    <audio id="audioPlayer" controls preload="metadata" style="width: 100%; margin-bottom: 1rem;">
                        <source src="${audioUrl}" type="${result.audioBlob.type}">
                        Your browser does not support the audio element.
                    </audio>
                    <div class="audio-actions">
                        <button class="btn btn-primary" onclick="app.downloadAudio()">
                            <i class="fas fa-download"></i>
                            Download Audio
                        </button>
                        <button class="btn btn-secondary" onclick="app.regenerateAudio()">
                            <i class="fas fa-redo"></i>
                            Regenerate
                        </button>
                        <button class="btn btn-secondary" onclick="app.testAudioPlayback()">
                            <i class="fas fa-play"></i>
                            Test Playback
                        </button>
                    </div>
                </div>
                <div class="audio-script">
                    <h4>Transcription Content:</h4>
                    <div class="script-content">
                        ${this.formatScriptText(result.script)}
                    </div>
                </div>
            </div>
        `;

        // Set up audio element event listeners for debugging
        setTimeout(() => {
            const audioElement = document.getElementById('audioPlayer');
            if (audioElement) {
                audioElement.addEventListener('loadstart', () => console.log('Audio: Load started'));
                audioElement.addEventListener('loadeddata', () => console.log('Audio: Data loaded'));
                audioElement.addEventListener('loadedmetadata', () => console.log('Audio: Metadata loaded, duration:', audioElement.duration));
                audioElement.addEventListener('canplay', () => console.log('Audio: Can start playing'));
                audioElement.addEventListener('canplaythrough', () => console.log('Audio: Can play through'));
                audioElement.addEventListener('error', (e) => console.error('Audio error:', e, audioElement.error));

                // Try to load the audio
                audioElement.load();
            }
        }, 100);
    }

    // Keep legacy method for backward compatibility
    displayPodcast(result) {
        return this.displayAudio(result);
    }

    formatTranscriptionText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/(Speaker \d+:)/g, '<strong class="speaker-label">$1</strong>');
    }

    formatSummaryText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>');
    }

    formatScriptText(text) {
        return text
            .replace(/\n/g, '<br>')
            .replace(/\[([^\]]+)\]/g, '<em class="stage-direction">[$1]</em>');
    }

    createPodcastScriptFromTranscription(transcription, summary = null) {
        console.log('Creating podcast script from transcription...');

        let script = '';

        // Add intro
        script += 'Welcome to this audio summary. ';

        if (summary) {
            // If we have a summary, use it as an introduction
            script += 'Here\'s a summary of the key points: ';
            script += summary + ' ';
            script += 'Now, let\'s hear the full conversation. ';
        }

        // Clean and format the transcription for better TTS
        let cleanedTranscription = transcription
            // Remove excessive line breaks
            .replace(/\n+/g, ' ')
            // Add pauses after sentences
            .replace(/\./g, '. ')
            .replace(/\?/g, '? ')
            .replace(/!/g, '! ')
            // Add pauses after speaker changes
            .replace(/(Speaker \d+:)/g, ' $1 ')
            // Clean up multiple spaces
            .replace(/\s+/g, ' ')
            .trim();

        script += cleanedTranscription;

        // Add outro
        script += ' Thank you for listening to this audio summary.';

        console.log('Generated podcast script, length:', script.length);
        return script;
    }

    downloadAudio() {
        if (this.currentAudio || this.currentPodcast) {
            const audio = this.currentAudio || this.currentPodcast;
            const fileName = audio.fileName || `audio_${new Date().toISOString().replace(/[:.]/g, '-')}.wav`;
            this.audioGenerator.createDownloadLink(audio.audioBlob, fileName);
            this.showToast('Audio download started', 'success');
        } else {
            this.showToast('No audio available to download', 'error');
        }
    }

    async regenerateAudio() {
        if (!this.currentTranscription) {
            this.showToast('No transcription available for audio generation', 'error');
            return;
        }

        try {
            await this.generateAudio();
        } catch (error) {
            console.error('Audio regeneration failed:', error);
            this.showToast(`Failed to regenerate audio: ${error.message}`, 'error');
        }
    }

    // Keep legacy methods for backward compatibility
    downloadPodcast() {
        return this.downloadAudio();
    }

    async regeneratePodcast() {
        return this.regenerateAudio();
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    testAudioPlayback() {
        const audioElement = document.getElementById('audioPlayer');
        if (audioElement) {
            console.log('Testing audio playback...');
            console.log('Audio element src:', audioElement.src);
            console.log('Audio element readyState:', audioElement.readyState);
            console.log('Audio element networkState:', audioElement.networkState);
            console.log('Audio element error:', audioElement.error);

            if (audioElement.readyState >= 2) {
                audioElement.play().then(() => {
                    console.log('Audio playback started successfully');
                    this.showToast('Audio playback test successful', 'success');
                }).catch(error => {
                    console.error('Audio playback failed:', error);
                    this.showToast(`Audio playback failed: ${error.message}`, 'error');
                });
            } else {
                this.showToast('Audio not ready for playback yet', 'warning');
            }
        } else {
            this.showToast('No audio element found', 'error');
        }
    }

    exportResults() {
        const results = {
            timestamp: new Date().toISOString(),
            transcription: this.currentTranscription,
            summary: this.currentSummary,
            speakers: this.currentSpeakers,
            podcast: this.currentPodcast ? {
                script: this.currentPodcast.script,
                metadata: this.currentPodcast.metadata
            } : null
        };

        const blob = new Blob([JSON.stringify(results, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `audio_summary_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`;
        a.click();
        
        URL.revokeObjectURL(url);
        this.showToast('Results exported successfully', 'success');
    }

    clearResults() {
        // Clear generated audio files
        if (this.audioGenerator) {
            this.audioGenerator.clearGeneratedFiles();
        }

        // Clean up audio URLs
        if (this.audioUrls) {
            this.audioUrls.forEach(url => URL.revokeObjectURL(url));
            this.audioUrls = [];
        }

        this.currentTranscription = null;
        this.currentSummary = null;
        this.currentSpeakers = null;
        this.currentAudio = null;
        this.currentPodcast = null;

        document.getElementById('transcriptionContent').innerHTML =
            '<p class="placeholder-text">Transcription will appear here after processing...</p>';
        document.getElementById('summaryContent').innerHTML =
            '<p class="placeholder-text">Summary will appear here after processing...</p>';
        document.getElementById('speakersContent').innerHTML =
            '<p class="placeholder-text">Speaker analysis will appear here after processing...</p>';
        document.getElementById('podcastContent').innerHTML = `
            <div class="audio-controls">
                <button id="generateAudioBtn" class="btn btn-primary">
                    <i class="fas fa-volume-up"></i>
                    Generate Audio
                </button>
            </div>
            <p class="placeholder-text">Generated audio will appear here...</p>
        `;

        // Re-attach event listener for generate audio button
        document.getElementById('generateAudioBtn').addEventListener('click', () => {
            this.generateAudio();
        });

        document.getElementById('exportResults').disabled = true;
        this.showToast('Results cleared', 'success');
    }

    switchTab(tabName) {
        // Remove active class from all tabs and panels
        document.querySelectorAll('.tab-btn').forEach(btn => btn.classList.remove('active'));
        document.querySelectorAll('.tab-panel').forEach(panel => panel.classList.remove('active'));
        
        // Add active class to selected tab and panel
        document.querySelector(`[data-tab="${tabName}"]`).classList.add('active');
        document.getElementById(`${tabName}Tab`).classList.add('active');
    }

    updateRecordingUI(isRecording) {
        document.getElementById('startRecording').disabled = isRecording;
        document.getElementById('stopRecording').disabled = !isRecording;
        document.getElementById('pauseRecording').disabled = !isRecording;
        
        const status = document.getElementById('recordingStatus');
        status.textContent = isRecording ? 'Recording...' : 'Ready to record';
        status.className = isRecording ? 'status-text recording' : 'status-text';
    }

    updatePauseButton() {
        const btn = document.getElementById('pauseRecording');
        const icon = btn.querySelector('i');
        
        if (this.audioProcessor.isPaused) {
            icon.className = 'fas fa-play';
            btn.innerHTML = '<i class="fas fa-play"></i> Resume';
        } else {
            icon.className = 'fas fa-pause';
            btn.innerHTML = '<i class="fas fa-pause"></i> Pause';
        }
    }

    startRecordingTimer() {
        this.recordingStartTime = Date.now();
        this.recordingTimer = setInterval(() => {
            this.updateRecordingTime();
        }, 1000);
    }

    stopRecordingTimer() {
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
            this.recordingTimer = null;
        }
    }

    updateRecordingTime() {
        if (!this.recordingStartTime) return;
        
        const elapsed = Math.floor((Date.now() - this.recordingStartTime) / 1000);
        const minutes = Math.floor(elapsed / 60);
        const seconds = elapsed % 60;
        
        document.getElementById('recordingTime').textContent = 
            `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    }

    handleKeyboardShortcuts(e) {
        if (e.ctrlKey || e.metaKey) {
            switch (e.key) {
                case 'r':
                    e.preventDefault();
                    if (!this.audioProcessor.isRecording) {
                        this.startRecording();
                    } else {
                        this.stopRecording();
                    }
                    break;
                case 'p':
                    e.preventDefault();
                    if (this.audioProcessor.isRecording) {
                        this.pauseRecording();
                    }
                    break;
                case 'Enter':
                    e.preventDefault();
                    if (this.currentRecording && !this.audioProcessor.isRecording) {
                        this.processAudio();
                    }
                    break;
            }
        }
    }

    updateUI() {
        // Update UI based on current state
        document.getElementById('processAudio').disabled = !this.currentRecording;
        document.getElementById('exportResults').disabled = !this.currentTranscription;
    }

    // Global utility functions
    showLoading(message = 'Loading...') {
        const overlay = document.getElementById('loadingOverlay');
        const text = document.getElementById('loadingText');
        text.textContent = message;
        overlay.classList.add('active');
    }

    hideLoading() {
        document.getElementById('loadingOverlay').classList.remove('active');
        this.updateProgress(0, '');
    }

    updateProgress(percentage, message) {
        const fill = document.getElementById('progressFill');
        const text = document.getElementById('loadingText');
        
        fill.style.width = `${percentage}%`;
        if (message) {
            text.textContent = message;
        }
    }

    showToast(message, type = 'info') {
        const container = document.getElementById('toastContainer');
        const toast = document.createElement('div');
        toast.className = `toast ${type}`;
        
        toast.innerHTML = `
            <i class="fas fa-${this.getToastIcon(type)}"></i>
            <span>${message}</span>
        `;
        
        container.appendChild(toast);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        }, 5000);
        
        // Remove on click
        toast.addEventListener('click', () => {
            if (toast.parentNode) {
                toast.parentNode.removeChild(toast);
            }
        });
    }

    getToastIcon(type) {
        switch (type) {
            case 'success': return 'check-circle';
            case 'error': return 'exclamation-circle';
            case 'warning': return 'exclamation-triangle';
            default: return 'info-circle';
        }
    }

    cleanup() {
        this.audioProcessor.cleanup();
        this.speakerDetection.cleanup();
        this.podcastGenerator.cleanup();
        
        if (this.recordingTimer) {
            clearInterval(this.recordingTimer);
        }
    }
}

// Initialize the application when the DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    window.app = new AudioSummarizerApp();
    
    // Make utility functions globally available
    window.showToast = (message, type) => window.app.showToast(message, type);
    window.updateProgress = (percentage, message) => window.app.updateProgress(percentage, message);
});

// Cleanup on page unload
window.addEventListener('beforeunload', () => {
    if (window.app) {
        window.app.cleanup();
    }
});
