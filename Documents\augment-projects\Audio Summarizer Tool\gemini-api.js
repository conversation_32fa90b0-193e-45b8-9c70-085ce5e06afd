/**
 * Gemini API Integration Module
 * Handles Google Gemini API calls for audio transcription and summarization
 */

class GeminiAPI {
    constructor() {
        this.apiKey = null;
        this.baseUrl = 'https://generativelanguage.googleapis.com/v1beta';
        this.model = 'gemini-2.0-flash-exp';
        this.audioModel = 'gemini-2.0-flash-exp';
        this.ttsModel = 'gemini-2.5-flash-preview-tts'; // Dedicated TTS model

        this.loadApiKey();
    }

    loadApiKey() {
        this.apiKey = localStorage.getItem('gemini_api_key');
        return !!this.apiKey;
    }

    saveApiKey(apiKey) {
        if (!apiKey || apiKey.trim() === '') {
            throw new Error('API key cannot be empty');
        }
        
        this.apiKey = apiKey.trim();
        localStorage.setItem('gemini_api_key', this.apiKey);
        this.showToast('API key saved successfully', 'success');
    }

    clearApiKey() {
        this.apiKey = null;
        localStorage.removeItem('gemini_api_key');
    }

    isConfigured() {
        return !!this.apiKey;
    }

    async validateApiKey() {
        if (!this.apiKey) {
            throw new Error('API key not configured');
        }

        try {
            const response = await fetch(`${this.baseUrl}/models?key=${this.apiKey}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`API validation failed: ${response.status} ${response.statusText}`);
            }

            return true;
        } catch (error) {
            console.error('API key validation failed:', error);
            throw new Error('Invalid API key or network error');
        }
    }

    async convertAudioToBase64(audioBlob) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => {
                const base64 = reader.result.split(',')[1]; // Remove data:audio/... prefix
                resolve(base64);
            };
            reader.onerror = reject;
            reader.readAsDataURL(audioBlob);
        });
    }

    async transcribeAudio(audioBlob, options = {}) {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key not configured');
        }

        if (!audioBlob || audioBlob.size === 0) {
            throw new Error('No audio data provided for transcription');
        }

        try {
            console.log('Starting transcription with audio blob size:', audioBlob.size);
            this.updateProgress(10, 'Converting audio...');

            // Convert audio to base64
            const audioBase64 = await this.convertAudioToBase64(audioBlob);
            console.log('Audio converted to base64, length:', audioBase64.length);

            this.updateProgress(30, 'Sending to Gemini API...');

            const requestBody = {
                contents: [{
                    parts: [
                        {
                            text: `Please transcribe this audio file and provide a detailed transcription. ${options.speakerIdentification ? 'If there are multiple speakers, identify them and label them as Speaker 1, Speaker 2, etc. Format each speaker\'s dialogue clearly with proper speaker labels.' : ''} Provide accurate transcription with proper punctuation and formatting.`
                        },
                        {
                            inline_data: {
                                mime_type: this.getMimeTypeFromBlob(audioBlob),
                                data: audioBase64
                            }
                        }
                    ]
                }],
                generationConfig: {
                    temperature: 0.1,
                    topK: 32,
                    topP: 1,
                    maxOutputTokens: 8192
                }
            };

            console.log('Sending request to Gemini API...');
            const response = await fetch(`${this.baseUrl}/models/${this.audioModel}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            this.updateProgress(70, 'Processing transcription...');

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                console.error('API Error:', errorData);
                throw new Error(`Transcription failed: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            console.log('Received response from Gemini API');

            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                console.error('Invalid API response:', data);
                throw new Error('Invalid response format from Gemini API');
            }

            const transcription = data.candidates[0].content.parts[0].text;
            console.log('Transcription completed, length:', transcription.length);

            this.updateProgress(100, 'Transcription complete');

            return {
                transcription: transcription,
                confidence: 0.95, // Gemini doesn't provide confidence scores
                language: 'auto-detected',
                speakers: options.speakerIdentification ? this.extractSpeakers(transcription) : null
            };

        } catch (error) {
            console.error('Transcription error:', error);
            this.updateProgress(0, 'Transcription failed');
            throw error;
        }
    }

    async summarizeText(text, options = {}) {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key not configured');
        }

        try {
            this.updateProgress(10, 'Preparing summary request...');

            const summaryPrompt = this.buildSummaryPrompt(text, options);

            const requestBody = {
                contents: [{
                    parts: [{
                        text: summaryPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.3,
                    topK: 32,
                    topP: 0.8,
                    maxOutputTokens: 4096
                }
            };

            this.updateProgress(30, 'Generating summary...');

            const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            this.updateProgress(70, 'Processing summary...');

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Summarization failed: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Invalid response format from Gemini API');
            }

            const summary = data.candidates[0].content.parts[0].text;
            
            this.updateProgress(100, 'Summary complete');
            
            return {
                summary: summary,
                keyPoints: this.extractKeyPoints(summary),
                wordCount: text.split(' ').length,
                summaryRatio: summary.split(' ').length / text.split(' ').length
            };

        } catch (error) {
            console.error('Summarization error:', error);
            throw error;
        }
    }

    async generatePodcastScript(summary, options = {}) {
        if (!this.isConfigured()) {
            throw new Error('Gemini API key not configured');
        }

        try {
            this.updateProgress(10, 'Creating podcast script...');

            const podcastPrompt = `
Transform the following summary into an engaging podcast script. 
Create a natural, conversational narrative suitable for text-to-speech conversion.
Include smooth transitions, engaging introductions, and clear conclusions.
Make it sound like a professional podcast host is presenting the information.

Style: ${options.style || 'Professional and engaging'}
Duration target: ${options.duration || '5-10 minutes'}
Tone: ${options.tone || 'Informative yet conversational'}

Summary to transform:
${summary}

Please format the output as a podcast script with clear sections and natural speech patterns.
`;

            const requestBody = {
                contents: [{
                    parts: [{
                        text: podcastPrompt
                    }]
                }],
                generationConfig: {
                    temperature: 0.7,
                    topK: 40,
                    topP: 0.9,
                    maxOutputTokens: 4096
                }
            };

            this.updateProgress(50, 'Generating script...');

            const response = await fetch(`${this.baseUrl}/models/${this.model}:generateContent?key=${this.apiKey}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            this.updateProgress(90, 'Finalizing script...');

            if (!response.ok) {
                const errorData = await response.json().catch(() => ({}));
                throw new Error(`Podcast script generation failed: ${response.status} ${response.statusText} - ${errorData.error?.message || 'Unknown error'}`);
            }

            const data = await response.json();
            
            if (!data.candidates || !data.candidates[0] || !data.candidates[0].content) {
                throw new Error('Invalid response format from Gemini API');
            }

            const script = data.candidates[0].content.parts[0].text;
            
            this.updateProgress(100, 'Podcast script ready');
            
            return {
                script: script,
                estimatedDuration: this.estimateDuration(script),
                wordCount: script.split(' ').length
            };

        } catch (error) {
            console.error('Podcast script generation error:', error);
            throw error;
        }
    }

    buildSummaryPrompt(text, options) {
        let prompt = `Please provide a comprehensive summary of the following text. `;
        
        if (options.speakerIdentification) {
            prompt += `Pay attention to different speakers and their contributions. `;
        }
        
        prompt += `Focus on key points, main themes, and important details. `;
        prompt += `Make the summary clear, concise, and well-structured.\n\n`;
        prompt += `Text to summarize:\n${text}`;
        
        return prompt;
    }

    extractSpeakers(transcription) {
        const speakerPattern = /Speaker \d+/gi;
        const speakers = [...new Set(transcription.match(speakerPattern) || [])];
        return speakers.map(speaker => ({
            id: speaker,
            segments: this.extractSpeakerSegments(transcription, speaker)
        }));
    }

    extractSpeakerSegments(transcription, speaker) {
        const segments = [];
        const lines = transcription.split('\n');
        let currentSegment = '';
        
        for (const line of lines) {
            if (line.includes(speaker)) {
                if (currentSegment) {
                    segments.push(currentSegment.trim());
                }
                currentSegment = line;
            } else if (currentSegment && line.trim()) {
                currentSegment += ' ' + line;
            }
        }
        
        if (currentSegment) {
            segments.push(currentSegment.trim());
        }
        
        return segments;
    }

    extractKeyPoints(summary) {
        // Simple extraction of bullet points or numbered items
        const lines = summary.split('\n');
        const keyPoints = [];
        
        for (const line of lines) {
            const trimmed = line.trim();
            if (trimmed.match(/^[\d\-\*•]/) || trimmed.includes('Key point') || trimmed.includes('Important')) {
                keyPoints.push(trimmed);
            }
        }
        
        return keyPoints.length > 0 ? keyPoints : [summary.substring(0, 200) + '...'];
    }

    estimateDuration(script) {
        // Rough estimation: average speaking rate is 150-160 words per minute
        const wordCount = script.split(' ').length;
        const minutes = Math.ceil(wordCount / 155);
        return `${minutes} minute${minutes !== 1 ? 's' : ''}`;
    }

    getMimeTypeFromBlob(blob) {
        return blob.type || 'audio/webm';
    }

    updateProgress(percentage, message) {
        if (window.updateProgress) {
            window.updateProgress(percentage, message);
        }
    }

    showToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`${type.toUpperCase()}: ${message}`);
        }
    }
}

// Export for use in other modules
window.GeminiAPI = GeminiAPI;
